--[[
    CoreClient.lua
    
    Main entry point for the client-side code. Initializes Knit and loads all controllers.
    
    This script:
    1. Sets up necessary game objects
    2. Initializes the Knit framework
    3. Loads and registers all Knit controllers
    4. Starts the Knit framework
]]

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Modules
local Packages = ReplicatedStorage:WaitForChild("Packages")
local Knit = require(Packages.knit)

-- Performance tracking
local startTime = tick()

-- Log initialization
print("[Client] Client initialization starting")

-- Initialize Knit controllers
local function initializeKnit()
    print("[Client] Initializing Knit controllers")
    
    -- Add all controllers from the Controllers directory
    local success, err = pcall(function()
        Knit.AddControllers(script.Parent.Controllers)
    end)
    
    if not success then
        warn("[Client] Error adding Knit controllers: " .. tostring(err))
        return false
    end
    
    return true
end

-- Start Knit
local function startKnit()
    print("[Client] Starting Knit")
    
    return Knit.Start()
        :andThen(function()
            print("[Client] Knit started successfully")
            return true
        end)
        :catch(function(err)
            warn("[Client] Failed to start Knit: " .. tostring(err))
            return false
        end)
end

-- Main execution
if initializeKnit() then
    startKnit():await()
    
    -- Log completion and performance
    local elapsedTime = tick() - startTime
    print(string.format("[Client] Client initialization complete (%.2f seconds)", elapsedTime))
else
    warn("[Client] Client initialization failed")
end

-- Set up error handling for runtime errors
RunService.Heartbeat:Connect(function()
    -- This empty connection ensures the script stays alive
    -- even if there's an error in the main thread
end)
