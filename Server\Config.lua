--[[
    Config.lua

    Centralized configuration module for the server-side scripts.
    Loads configuration from env.lua and provides a clean interface for accessing it.

    Usage:
    local Config = require(script.Parent.Config)
    local apiUrl = Config:GetValue("API", "BaseURL")
]]

local ServerScriptService = game:GetService("ServerScriptService")
local HttpService = game:GetService("HttpService")

-- Import env.lua
local env = require(script.Parent.env)

-- Module table
local Config = {}

-- Default configuration values
Config.Values = {
    -- API configuration
    API = {
        BaseURL = env.apiurl or "https://tvc.drandex.org",
        Timeout = 10,
        RetryCount = 3,
        RetryDelay = 1,
        UseAuth = true,
        Debug = false,
        SharedSecretKey = env.apikey or "3d95283f2d38592f745f138c3b3f656481bd9e77a3aef6bc491db014c35bf7ae",
        TimeWindow = 300, -- 5 minutes
        Endpoints = {
            GetPlayerData = "/playerdata/roblox/%s",
            UpdatePlayerData = "/playerdata/roblox/%s",
            CreatePlayerData = "/playerdata/roblox",
            ReleasePlayerData = "/playerdata/roblox/%s/release",
            ForceReleasePlayerData = "/playerdata/roblox/%s/force_release",
            DirectUnlockPlayerData = "/playerdata/roblox/%s/direct_unlock"
        }
    },

    -- DataStore configuration
    DataStore = {
        UseDataStore = true,
        ProfileStoreKey = env.profileStore or "playerdata1",
        AutoSaveInterval = 300, -- 5 minutes
        ProfileTemplate = {
            -- Default profile template
            ["__migrated"] = 0,
            ["admin"] = false,
            ["banned"] = false,
            ["coins"] = 0,
            ["dev"] = false,
            ["gems"] = 0,
            ["inventory"] = {},
            ["level"] = 1,
            ["settings"] = {
                ["fov"] = 70,
                ["lowDetailMode"] = false,
                ["musicVolume"] = 100,
                ["ovhcolor"] = "1, 1, 1",
                ["plrs"] = false,
                ["potatoMode"] = false,
                ["rainbow"] = true,
                ["sfxVolume"] = 100,
                ["tags"] = false,
                ["volume"] = 50
            },
            ["vip"] = false,
            ["xp"] = 1
        }
    },

    -- Chatlog configuration
    Chatlog = {
        BatchSize = 10,
        SendInterval = 60,
        MaxRetries = 3,
        RetryDelay = 5,
        Endpoint = "/chatlogs/roblox",
        Debug = true,
        CacheClearInterval = 3600
    },

    -- Logger configuration
    Logger = {
        Level = 2, -- INFO
        ShowTimestamp = true,
        ShowLevel = true,
        UseOutput = false,
        Prefix = "[Server]"
    },

    -- Content IDs
    ContentID = {
        LoadingScreen = env.contentid.loadingscreen or 7248350055
    },

    -- General configuration
    General = {
        AutoSaveInterval = 300, -- 5 minutes
        SaveOnLeave = true,
        LoadOnJoin = true,
        Debug = false
    }
}

-- Get a configuration value
function Config:GetValue(category, key)
    if not category then
        return nil
    end

    if not key then
        return self.Values[category]
    end

    if not self.Values[category] then
        warn("Config category not found: " .. category)
        return nil
    end

    if self.Values[category][key] == nil then
        warn("Config key not found: " .. category .. "." .. key)
        return nil
    end

    return self.Values[category][key]
end

-- Set a configuration value
function Config:SetValue(category, key, value)
    if not category or not key then
        return false
    end

    if not self.Values[category] then
        self.Values[category] = {}
    end

    self.Values[category][key] = value
    return true
end

-- Get API configuration
function Config:GetAPIConfig()
    return self.Values.API
end

-- Get DataStore configuration
function Config:GetDataStoreConfig()
    return self.Values.DataStore
end

-- Get Chatlog configuration
function Config:GetChatlogConfig()
    return self.Values.Chatlog
end

-- Get Logger configuration
function Config:GetLoggerConfig()
    return self.Values.Logger
end

-- Validate configuration
function Config:Validate()
    -- Check required values
    local requiredValues = {
        {"API", "BaseURL"},
        {"API", "SharedSecretKey"},
        {"DataStore", "ProfileStoreKey"}
    }

    local valid = true
    for _, value in ipairs(requiredValues) do
        local category, key = value[1], value[2]
        if not self:GetValue(category, key) then
            warn("Missing required configuration value: " .. category .. "." .. key)
            valid = false
        end
    end

    return valid
end

-- Initialize the Config module
function Config:Initialize()
    -- Validate configuration
    local valid = self:Validate()
    if not valid then
        warn("Configuration validation failed. Some required values are missing.")
    end

    return self
end

return Config:Initialize()
