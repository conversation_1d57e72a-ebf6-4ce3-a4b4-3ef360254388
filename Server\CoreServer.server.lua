--[[
    CoreServer.server.lua (No changes needed)

    This script is already configured correctly. It will load our new
    PlayerDataService once it is placed in the "Knit" subfolder.
]]

-- Services
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService("ServerScriptService")
local RunService = game:GetService("RunService")

-- Modules
local Packages = ReplicatedStorage:WaitForChild('Packages')
local Knit = require(Packages.knit)
local Server = script.Parent
local services = ServerScriptService.Server.Services
local util = require(Server.util)
local Config = require(Server.Config)

-- Performance tracking
local startTime = tick()

-- Log initialization
util.log("Server initialization starting", nil, script.Name)

-- Pre-setup: Create necessary game objects
local function setupGameObjects()
	util.log("Setting up game objects", nil, script.Name)

	-- Set up load zones
	local zones = workspace:FindFirstChild("LoadTemp")
	if zones then
		zones.Name = "LoadZones"
		zones.Parent = ReplicatedStorage
		util.log("LoadZones set up successfully", nil, script.Name)
	else
		util.log("Warning: LoadTemp not found in workspace", nil, script.Name)
	end
end

-- Initialize Knit services
local function initializeKnit()
	util.log("Initializing Knit services", nil, script.Name)

	-- Add all services from the Knit directory
	local success, err = pcall(function()
		Knit.AddServices(services.Knit) -- This correctly targets the subfolder
	end)

	if not success then
		util.log("Error adding Knit services: %s", {err}, script.Name)
		warn("Failed to add Knit services: " .. tostring(err))
		return false
	end

	return true
end

-- Start Knit
local function startKnit()
	util.log("Starting Knit", nil, script.Name)

	return Knit.Start()
		:andThen(function()
			util.log("Knit started successfully", nil, script.Name)
			return true
		end)
		:catch(function(err)
			util.log("Error starting Knit: %s", {err}, script.Name)
			warn("Failed to start Knit: " .. tostring(err))
			return false
		end)
end

-- Main execution
setupGameObjects()

if initializeKnit() then
	startKnit():await()

	-- Log completion and performance
	local elapsedTime = tick() - startTime
	util.log("Server initialization complete (%.2f seconds)", {elapsedTime}, script.Name)
else
	util.log("Server initialization failed", nil, script.Name)
end

-- Set up error handling for runtime errors
RunService.Heartbeat:Connect(function()
	-- This empty connection ensures the script stays alive
	-- even if there's an error in the main thread
end)