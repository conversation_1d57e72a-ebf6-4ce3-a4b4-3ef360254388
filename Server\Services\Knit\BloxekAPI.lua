--[[
    FakeBloxekAPI (Compatibility Layer)

    [!] IMPORTANT [!]
    This is a temporary compatibility module, also known as an "Adapter".
    Its purpose is to translate calls from old, legacy scripts expecting "bloxekapi"
    to the new, modern "PlayerDataService".

    This allows the rest of the game to function without needing an immediate full-scale refactor.

    How it works:
    - It registers itself to Knit with the OLD name: "bloxe<PERSON><PERSON>".
    - It provides all the functions the old service had.
    - Internally, it calls the REAL PlayerDataService and formats the data to match
      what the old scripts expect.

    Long-term goal should be to update legacy scripts to use PlayerDataService directly
    and eventually remove this compatibility layer.
]]

-- Packages
local Packages = game.ReplicatedStorage:WaitForChild("Packages")
local Knit = require(Packages.knit)
local Promise = require(Packages.promise)

-- Service Definition
local FakeBloxekAPI = Knit.CreateService {
	-- We use the OLD service name to intercept calls from legacy scripts
	Name = "bloxekapi",
	Client = {}
}

-- This will hold the reference to the real service
FakeBloxekAPI.PlayerDataService = nil

--[[
    [SERVER] Gets player data from the new service and wraps it in the old format.
]]
function FakeBloxekAPI:GetPlayerData(player)
	return Promise.new(function(resolve)
		-- Ensure the real service is available before proceeding
		if not self.PlayerDataService then
			warn("[FakeBloxekAPI] PlayerDataService not ready, waiting...")
			while not self.PlayerDataService do
				wait(1)
				print("retrying")
			end
		end

		-- Call the real service
		self.PlayerDataService:GetPlayerData(player):andThen(function(playerData)
			if playerData then
				-- The old service returned a table with a "Data" key.
				-- We replicate that structure here for compatibility.
				resolve({ Data = playerData })
			else
				-- If PlayerDataService returns nil (timeout/error), reject the promise
				-- This will cause the calling code to handle the failure appropriately
				warn("[FakeBloxekAPI] PlayerDataService returned nil for " .. player.Name .. " - ProfileStore failed to load profile")
				resolve(nil)
			end
		end)
	end)
end

--[[
    [SERVER] Updates user settings by passing the call directly to the new service.
    Note the function name matches the OLD service ("UpdateUserSettings").
]]
function FakeBloxekAPI:UpdateUserSettings(player, setting, value)
	if not self.PlayerDataService then
		return warn("[FakeBloxekAPI] PlayerDataService not ready, cannot UpdateUserSettings.")
	end

	-- The new service uses the name "UpdateUserSetting" (singular).
	self.PlayerDataService:UpdateUserSetting(player, setting, value)
end

--[[
    [SERVER] Legacy function. Always returns false as there is no backup system anymore.
]]
function FakeBloxekAPI:IsUsingBackupDataStore(userId)
	-- This concept is obsolete. Returning 'false' is the safest value for legacy scripts.
	return false
end

--[[
    [SERVER] Legacy function. Always returns a perfect status.
]]
function FakeBloxekAPI:GetBackendStatus()
	-- This concept is obsolete. Return a "healthy" status to prevent legacy
	-- UI or logic from thinking there's an issue.
	return {
		isAvailable = true,
		lastCheckTime = os.time(),
		pendingSyncCount = 0,
		usingBackupCount = 0
	}
end


--// Client Functions
--------------------------------------------------------------------------------
-- These functions just pass the request to their server-side counterparts.

function FakeBloxekAPI.Client:GetPlayerData()
	return self.Server:GetPlayerData(self.Player)
end

function FakeBloxekAPI.Client:UpdateUserSettings(setting, value)
	-- The old client function didn't require the player argument.
	return self.Server:UpdateUserSettings(self.Player, setting, value)
end

function FakeBloxekAPI.Client:IsUsingBackupDataStore()
	return self.Server:IsUsingBackupDataStore(self.Player.UserId)
end

function FakeBloxekAPI.Client:GetBackendStatus()
	return self.Server:GetBackendStatus()
end


--// Knit Integration
--------------------------------------------------------------------------------

function FakeBloxekAPI:KnitStart()
	-- Get the real PlayerDataService so we can forward calls to it.
	self.PlayerDataService = Knit.GetService("PlayerDataService")
	print("[FakeBloxekAPI] Compatibility layer started. Legacy calls will now be redirected to PlayerDataService.")
end

return FakeBloxekAPI