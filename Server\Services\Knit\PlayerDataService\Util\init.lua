--[[
    Util

    A collection of general-purpose utility functions for use throughout the game.
    This is a shared module, accessible by both the server and client.
]]

local Util = {}

--[[
    Recursively creates a complete, independent copy of a table.
    @param original [table] The table to copy.
    @returns [table] A new table identical to the original.
]]
function Util.DeepCopy(original)
	local copy
	if type(original) == "table" then
		copy = {}
		for key, value in pairs(original) do
			copy[key] = Util.DeepCopy(value)
		end
	else
		copy = original
	end
	return copy
end

--[[
    Recursively merges two tables. Keys from the second table will
    overwrite keys in the first.
    @param t1 [table] The base table.
    @param t2 [table] The table to merge in, overwriting base values.
    @returns [table] A new, merged table.
]]
function Util.MergeTables(t1, t2)
	local result = Util.DeepCopy(t1)
	for key, value in pairs(t2) do
		if type(value) == "table" and type(result[key]) == "table" then
			result[key] = Util.MergeTables(result[key], value)
		else
			result[key] = value -- t2's value overwrites t1's
		end
	end
	return result
end

--[[
    Formats a number with commas as thousands separators.
    @param num [number] The number to format.
    @returns [string] The comma-separated number string.
]]
function Util.FormatNumber(num)
	local formatted = tostring(math.floor(num))
	local k
	while true do
		formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", "%1,%2")
		if k == 0 then
			break
		end
	end
	return formatted
end

--[[
    Formats a duration in seconds into a MM:SS string.
    @param seconds [number] The total seconds.
    @returns [string] The formatted "MM:SS" string.
]]
function Util.FormatTime(seconds)
	local s = tonumber(seconds) or 0
	local minutes = math.floor(s / 60)
	local remainingSeconds = s % 60
	return string.format("%02d:%02d", minutes, remainingSeconds)
end

--[[
    Checks if a value exists in an array-like table.
    @param tbl [table] The array to search in.
    @param value [any] The value to search for.
    @returns [boolean] True if the value is found.
]]
function Util.Contains(tbl, value)
	for _, v in ipairs(tbl) do
		if v == value then
			return true
		end
	end
	return false
end

return Util