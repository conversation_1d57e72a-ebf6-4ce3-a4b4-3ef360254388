--[[
    Config Module

    Configuration management for the Roblox client agent.
]]

-- Module table
local Config = {}

-- Default configuration
Config.Settings = {
    -- API configuration
    API = {
        BaseURL = "https://tvc.drandex.org",
        Timeout = 10,
        RetryCount = 3,
        RetryDelay = 1,
        UseAuth = true,
        Debug = true,
        SharedSecretKey = "3d95283f2d38592f745f138c3b3f656481bd9e77a3aef6bc491db014c35bf7ae", -- This should match the server's SHARED_SECRET_KEY
        TimeWindow = 300, -- 5 minutes, should match the server's TIME_WINDOW
        Endpoints = {
            GetPlayerData = "/playerdata/roblox/%s",
            UpdatePlayerData = "/playerdata/roblox/%s",
            CreatePlayerData = "/playerdata/roblox",
            ReleasePlayerData = "/playerdata/roblox/%s/release",
            ForceReleasePlayerData = "/playerdata/roblox/%s/force_release",
            DirectUnlockPlayerData = "/playerdata/roblox/%s/direct_unlock"
        }
    },

    -- DataStore configuration
    DataStore = {
        StoreName = "PlayerData",
        Scope = "Global",
        RetryCount = 3,
        RetryDelay = 1,
        Debug = false
    },

    -- Chatlog configuration
    Chatlog = {
        BatchSize = 20,           -- Number of messages to batch before sending
        SendInterval = 60,        -- Seconds between sends if batch size not reached
        MaxRetries = 3,           -- Maximum number of retries for failed sends
        RetryDelay = 5,           -- Seconds between retries
        Endpoint = "/chatlogs/roblox",   -- API endpoint for chat logs
        Debug = true,             -- Debug mode
        SentMessagesMaxSize = 1000, -- Maximum size of the sent messages cache to prevent memory issues
        CacheClearInterval = 3600  -- Clear the sent messages cache every hour (3600 seconds)
    },

    -- Logger configuration
    Logger = {
        Level = 2,  -- INFO
        ShowTimestamp = true,
        ShowLevel = true,
        UseOutput = false,
        Prefix = "[ClientAgent]"
    },

    -- General configuration
    General = {
        AutoSaveInterval = 300,  -- 5 minutes
        SaveOnLeave = true,
        LoadOnJoin = true,
        UseDataStore = true,
        Debug = false
    }
}

-- Initialize the Config module
function Config:Initialize(config)
    -- Apply configuration
    if config then
        self:ApplyConfig(config)
    end

    return self
end

-- Apply configuration
function Config:ApplyConfig(config)
    -- Deep merge configuration
    local Utils = require(script.Parent)

    if config.API then
        self.Settings.API = Utils:MergeTables(self.Settings.API, config.API)
    end

    if config.DataStore then
        self.Settings.DataStore = Utils:MergeTables(self.Settings.DataStore, config.DataStore)
    end

    if config.Chatlog then
        self.Settings.Chatlog = Utils:MergeTables(self.Settings.Chatlog, config.Chatlog)
    end

    if config.Logger then
        self.Settings.Logger = Utils:MergeTables(self.Settings.Logger, config.Logger)
    end

    if config.General then
        self.Settings.General = Utils:MergeTables(self.Settings.General, config.General)
    end

    return self.Settings
end

-- Get API configuration
function Config:GetAPIConfig()
    return self.Settings.API
end

-- Get DataStore configuration
function Config:GetDataStoreConfig()
    return self.Settings.DataStore
end

-- Get Chatlog configuration
function Config:GetChatlogConfig()
    return self.Settings.Chatlog
end

-- Get Logger configuration
function Config:GetLoggerConfig()
    return self.Settings.Logger
end

-- Get General configuration
function Config:GetGeneralConfig()
    return self.Settings.General
end

-- Get full configuration
function Config:GetConfig()
    return self.Settings
end

-- Update API configuration
function Config:UpdateAPIConfig(config)
    local Utils = require(script.Parent)
    self.Settings.API = Utils:MergeTables(self.Settings.API, config)
    return self.Settings.API
end

-- Update DataStore configuration
function Config:UpdateDataStoreConfig(config)
    local Utils = require(script.Parent)
    self.Settings.DataStore = Utils:MergeTables(self.Settings.DataStore, config)
    return self.Settings.DataStore
end

-- Update Chatlog configuration
function Config:UpdateChatlogConfig(config)
    local Utils = require(script.Parent)
    self.Settings.Chatlog = Utils:MergeTables(self.Settings.Chatlog, config)
    return self.Settings.Chatlog
end

-- Update Logger configuration
function Config:UpdateLoggerConfig(config)
    local Utils = require(script.Parent)
    self.Settings.Logger = Utils:MergeTables(self.Settings.Logger, config)
    return self.Settings.Logger
end

-- Update General configuration
function Config:UpdateGeneralConfig(config)
    local Utils = require(script.Parent)
    self.Settings.General = Utils:MergeTables(self.Settings.General, config)
    return self.Settings.General
end

-- Return the module
return Config
