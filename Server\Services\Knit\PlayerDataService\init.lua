--[[
    PlayerDataService

    Handles all player data loading and saving using ProfileStore.
    This service is the single source of truth for player profiles.
    Verified and updated according to official ProfileStore documentation.
]]

-- (Optional) Set to true for detailed logging in the console.
_G.DEBUG_MODE = true

-- Services
local Players = game:GetService("Players")

-- Packages
local Packages = game.ReplicatedStorage:WaitForChild("Packages")
local Knit = require(Packages.knit)
local Promise = require(Packages.promise)
local ProfileStore = require(game.ServerStorage:WaitForChild('Packages'):WaitForChild('ProfileStore')) -- Assuming ProfileStore is in ReplicatedStorage/Packages

-- Service Definition
local PlayerDataService = Knit.CreateService {
	Name = "PlayerDataService",
	Client = {}
}

--// Private Variables
--------------------------------------------------------------------------------

-- A simple, standardized logger
local function log(level, message)
	if level == "Error" or level == "Warning" or _G.DEBUG_MODE then
		print(`[PlayerDataService] [{level}]: {message}`)
	end
end

-- The data structure for a brand new player
local DEFAULT_PLAYER_DATA = {
	Level = 1,
	XP = 0,
	-- NOTE: Renamed to match old data structure to prevent data loss.
	-- If starting fresh, you might use "Badges", "GamePasses", etc.
	badges = {},
	gamepasses = {},
	-- Renamed from Metadata for consistency
	metadata = {
		ChatsSent = 0,
		LastJoin = 0,
		RobuxSpent = 0,
		SessionLoadCount = 0,
		TimePlayed = 0,
		__migrated_from_ps = false -- Keep migration flag if needed
	},
	owned = {
		animations = {},
		badges = {},
		collectibles = {
			menofsnow = {},
			vibecoins = {}
		},
		gamepasses = {},
		items = {},
		rotech = {},
		tags = {}
	},
	settings = {
		fov = 70,
		lowDetailMode = false,
		musicVolume = 100,
		ovhcolor = "1, 1, 1",
		plrs = false,
		potatoMode = false,
		rainbow = true,
		sfxVolume = 100,
		tags = false,
		volume = 50
	},
	vip = false,
}

-- Create the ProfileStore using the correct constructor from the docs
local MainProfileStore = ProfileStore.New("PlayerData", DEFAULT_PLAYER_DATA)

-- Cache for active player profiles, using UserId as the key for server-side flexibility.
local PlayerProfiles = {}

--// Private Functions
--------------------------------------------------------------------------------

--[[
    Loads a player's profile when they join the game.
    Handles session locking, data migration, and race conditions.
    @param player [Player] The player instance.
]]
function PlayerDataService:_onPlayerAdded(player)
	local userId = player.UserId

	-- Start a profile session for this player's data:
	local profile = MainProfileStore:StartSessionAsync(`{player.UserId}`, {
		-- This function is called repeatedly while waiting for the profile to load.
		-- If it returns true, the loading process is cancelled.
		Cancel = function()
			return player.Parent ~= Players
		end,
	})

	-- Handle new profile session or failure:
	if profile ~= nil then
		-- (BEST PRACTICE) Add UserId for GDPR compliance.
		profile:AddUserId(player.UserId)

		-- (BEST PRACTICE) Fill in missing variables from the template. If you add a new
		-- value to DEFAULT_PLAYER_DATA, this will add it to existing player data.
		profile:Reconcile()

		-- (BEST PRACTICE) Listen for when the session is ended from another server.
		profile.OnSessionEnd:Connect(function()
			PlayerProfiles[userId] = nil
			player:Kick("Your profile was loaded on another server. Please rejoin.")
		end)

		-- Check if the player is still in the game after data has loaded.
		if player.Parent == Players then
			PlayerProfiles[userId] = profile
			log("Info", `Profile loaded for {player.Name}!`)

			-- Update metadata
			profile.Data.metadata.SessionLoadCount = (profile.Data.metadata.SessionLoadCount or 0) + 1
			profile.Data.metadata.LastJoin = os.time()
		else
			-- The player left before the profile finished loading.
			log("Info", `Player {player.Name} left before profile could be cached. Releasing.`)
			profile:EndSession()
		end
	else
		-- This should only happen during server shutdown or critical datastore failure.
		log("Error", `Profile for {player.Name} failed to load. Kicking.`)
		player:Kick("Your data failed to load. Please rejoin the game.")
	end
end

--[[
    Saves and releases a player's profile when they leave.
    @param player [Player] The player instance.
]]
function PlayerDataService:_onPlayerRemoving(player)
	local userId = player.UserId
	local profile = PlayerProfiles[userId]

	if profile then
		-- The EndSession() method handles saving the data to Roblox DataStores.
		profile:EndSession()
		-- The OnSessionEnd event will handle cleaning up the cache.
		log("Info", `Session ended for {player.Name}.`)
	else
		log("Warning", `Could not find profile for leaving player {player.Name}.`)
	end
end

--// Public Service Methods
--------------------------------------------------------------------------------

function PlayerDataService:GetPlayerData(player)
	-- Get the userId consistently
	local userId = typeof(player) == "Instance" and player.UserId or player
	return Promise.new(function(resolve)
		for i = 1, 60 do -- Wait up to 60 seconds for profile to load
			if PlayerProfiles[userId] then
				resolve(PlayerProfiles[userId].Data)
				return
			end
			task.wait(1)
		end
		log("Warning", `GetPlayerData: Timed out waiting for data for UserId {userId}`)
		resolve(nil)
	end)
end

function PlayerDataService:UpdateUserSetting(player, settingName, value)
	-- Implementation remains the same.
	local userId = typeof(player) == "number" and player or player.UserId
	local profile = PlayerProfiles[userId]

	if not profile then
		log("Warning", `Cannot update setting for UserId {userId}: Profile not loaded.`)
		return
	end
	if not profile.Data.settings or profile.Data.settings[settingName] == nil then
		log("Warning", `Cannot update setting '{settingName}' for UserId {userId}: Setting does not exist.`)
		return
	end
	if type(profile.Data.settings[settingName]) ~= type(value) then
		log("Warning", `Cannot update setting '{settingName}' for UserId {userId}: Type mismatch.`)
		return
	end

	profile.Data.settings[settingName] = value
	log("Debug", `Updated setting '{settingName}' to '{tostring(value)}' for UserId {userId}`)
end

--// Client-Facing Methods
--------------------------------------------------------------------------------

function PlayerDataService.Client:GetPlayerData()
	return self.Server:GetPlayerData(self.Player)
end

function PlayerDataService.Client:UpdateUserSetting(settingName, value)
	self.Server:UpdateUserSetting(self.Player, settingName, value)
end

--// Knit Integration
--------------------------------------------------------------------------------

function PlayerDataService:KnitStart()
	log("Info", "Service starting...")

	-- Handle players who join after the script runs
	Players.PlayerAdded:Connect(function(player)
		task.spawn(function() self:_onPlayerAdded(player) end)
	end)

	-- Handle players who are already in the game when the script runs
	for _, player in ipairs(Players:GetPlayers()) do
		task.spawn(function() self:_onPlayerAdded(player) end)
	end

	-- Handle player leaving
	Players.PlayerRemoving:Connect(function(player)
		self:_onPlayerRemoving(player)
	end)

	log("Info", "Service started and connected to player events.")
end

return PlayerDataService