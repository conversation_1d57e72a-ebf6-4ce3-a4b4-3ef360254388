--[[
    RegionItemService.lua
    
    A Knit service that manages region-based item distribution.
    This service detects when players enter specific regions and gives them
    appropriate items, removing the items when they leave the region.
    
    Features:
    - Configurable regions and items
    - Efficient player detection using Region3
    - Automatic item management (giving/removing)
]]

-- Services
local Players = game:GetService("Players")
local ServerStorage = game:GetService("ServerStorage")
local Workspace = game:GetService("Workspace")

-- Modules
local Packages = game:GetService("ReplicatedStorage"):WaitForChild("Packages")
local Knit = require(Packages.knit)

-- Create the service
local RegionItemService = Knit.CreateService {
    Name = "RegionItemService",
    Client = {}
}

-- Private variables
local regionConfigs = {}
local playerRegions = {}
local playerItems = {}

-- Initialize the service
function RegionItemService:KnitInit()
    -- Wait for the region boxes folder
    self.regionFolder = Workspace:WaitForChild("regionBoxes")
    
    -- Set up region configurations
    self:SetupRegionConfigs()
end

-- Start the service
function RegionItemService:KnitStart()
    -- Get other services if needed
    self.RunService = Knit.GetService("runservice")
    
    -- Set up player joining/leaving
    Players.PlayerAdded:Connect(function(player)
        playerRegions[player] = {}
        playerItems[player] = {}
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:CleanupPlayer(player)
    end)
    
    -- Handle existing players
    for _, player in ipairs(Players:GetPlayers()) do
        playerRegions[player] = {}
        playerItems[player] = {}
    end
    
    -- Set up region checking
    self:SetupRegionChecking()
end

-- Set up region configurations
function RegionItemService:SetupRegionConfigs()
    -- Define region configurations
    regionConfigs = {
        dojoBox = {
            itemName = "dojosword",
            itemSource = ServerStorage.Items
        },
        skateBox = {
            itemName = "Skateboard",
            itemSource = ServerStorage.Items
        }
        -- Add more regions as needed
    }
end

-- Set up region checking
function RegionItemService:SetupRegionChecking()
    -- Add to the RunService stepped loop
    self.RunService.stepped["RegionItemService"] = function()
        self:CheckAllRegions()
    end
end

-- Check all regions for all players
function RegionItemService:CheckAllRegions()
    for regionName, config in pairs(regionConfigs) do
        local regionPart = self.regionFolder:FindFirstChild(regionName)
        
        if regionPart then
            local region = Region3.new(
                regionPart.Position - (regionPart.Size/2), 
                regionPart.Position + (regionPart.Size/2)
            )
            
            for _, player in ipairs(Players:GetPlayers()) do
                if player.Character then
                    self:CheckPlayerInRegion(player, region, regionName, config)
                end
            end
        end
    end
end

-- Check if a player is in a specific region
function RegionItemService:CheckPlayerInRegion(player, region, regionName, config)
    local inRegion = false
    local playerParts = {}
    
    -- Get all parts from the player's character
    for _, part in ipairs(player.Character:GetChildren()) do
        if part:IsA("BasePart") then
            table.insert(playerParts, part)
        end
    end
    
    -- Check if any parts are in the region
    local partsInRegion = Workspace:FindPartsInRegion3WithWhiteList(region, playerParts)
    
    if #partsInRegion > 0 then
        inRegion = true
    end
    
    -- Update player region status
    local wasInRegion = playerRegions[player][regionName] or false
    playerRegions[player][regionName] = inRegion
    
    -- Handle region entry/exit
    if inRegion and not wasInRegion then
        self:GiveRegionItem(player, regionName, config)
    elseif not inRegion and wasInRegion then
        self:RemoveRegionItem(player, regionName)
    end
end

-- Give a region-specific item to a player
function RegionItemService:GiveRegionItem(player, regionName, config)
    if not playerItems[player][regionName] then
        local item = config.itemSource:FindFirstChild(config.itemName)
        
        if item then
            local clone = item:Clone()
            clone.Parent = player.Character
            playerItems[player][regionName] = clone
        end
    end
end

-- Remove a region-specific item from a player
function RegionItemService:RemoveRegionItem(player, regionName)
    local item = playerItems[player][regionName]
    
    if item then
        item:Destroy()
        playerItems[player][regionName] = nil
    end
end

-- Clean up a player when they leave
function RegionItemService:CleanupPlayer(player)
    -- Remove all items
    for regionName, item in pairs(playerItems[player] or {}) do
        if item then
            item:Destroy()
        end
    end
    
    -- Clear player data
    playerRegions[player] = nil
    playerItems[player] = nil
end

-- Public method to get a player's current regions
function RegionItemService:GetPlayerRegions(player)
    return playerRegions[player] or {}
end

-- Return the service
return RegionItemService
