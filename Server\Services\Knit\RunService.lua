local Packages = game:GetService('ReplicatedStorage'):WaitForChild('Packages')
local RunService = game:GetService('RunService')
local knit = require(Packages.knit)
local controller = knit.CreateService { Name = "runservice" }

controller.stepped = {}

function steppedLoop()
    for _, mod in pairs(controller.stepped) do
        if mod then
            mod()
        end
    end
end

function controller:KnitStart()
    RunService.Stepped:Connect(steppedLoop)
end

return controller