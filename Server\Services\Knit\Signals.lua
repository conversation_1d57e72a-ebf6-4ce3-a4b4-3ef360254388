local Packages = game.ReplicatedStorage:WaitForChild('Packages')
local knit = require(Packages.knit)
local service = knit.CreateService { Name = "signals" }
local comm = require(Packages.Comm)

local ServerComm = comm.ServerComm.new(game:GetService("ReplicatedStorage"))

service.Signals = {}
service.Properties = {}

function service:CreateSignal(name)
    service.Signals[name] = ServerComm:CreateSignal(name)
    return service.Signals[name]
end

function service.property(name)
    service.Properties = ServerComm:CreateProperty(name)
end

function service:BindFunction(name,func)
    return ServerComm:BindFunction(name,func)
end

function service:KnitStart()
    --self:<PERSON>reateSignal('SaveSantaRemove')
end

return service