local Packages = game.ReplicatedStorage:WaitForChild('Packages')
local knit = require(Packages.knit)
local service = knit.CreateService { Name = "teleports", Client = {} }

function service.Client:teleport(plr, part)
    if type(part) == "string" then
        for _, child in ipairs(workspace.Teleports:GetChildren()) do
            if child.Name == part then
                return child.teleport.CFrame
            end
        end
    end

    local receiver = part.Parent:FindFirstChild("receiver" .. string.sub(part.Name, 9))
    if receiver then
        return receiver.CFrame
    end
end

return service