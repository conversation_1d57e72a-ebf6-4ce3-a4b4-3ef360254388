--[[
    ItemHandler Module
    
    Handles item management for players, including:
    - Placing items in player backpacks
    - Giving items to players based on their profile
    - Setting up shop functionality
    - Equipping and unequipping items
]]

local ItemHandler = {}

-- Services
local BadgeService = game:GetService("BadgeService")
local MarketplaceService = game:GetService("MarketplaceService")
local ServerScriptService = game:GetService("ServerScriptService")
local ServerStorage = game:GetService("ServerStorage")

--[[
    Set up shop functionality
    @param gamepasses - Table of gamepasses
    @param service - The UserContent service
    @param data - Data service
]]
function ItemHandler:SetUpShop(gamepasses, service, data)
    -- This function is currently commented out in the original code
    -- Keeping the function signature for compatibility
end

--[[
    Get the length of a table
    @param tbl - The table to get the length of
    @return number - The length of the table
]]
local function GetLength(tbl)
    local length = 0
    
    for _ in pairs(tbl) do
        length = length + 1
    end
    
    return length
end

--[[
    Place an item in a player's backpack
    @param player - The player to give the item to
    @param itemName - The name of the item to give
]]
function ItemHandler:PlaceItem(player, itemName)
    for _, item in ipairs(ServerStorage.Items:GetChildren()) do
        local itemMatches = string.lower(item.Name) == string.lower(itemName)
        local oldNameMatches = item:GetAttribute("oldName") ~= nil and 
                              string.lower(item:GetAttribute("oldName")) == string.lower(itemName) and 
                              item:GetAttribute("GamepassName") ~= nil
        
        if itemMatches or oldNameMatches then
            -- Check for duplicates
            local duplicate = false
            for _, backpackItem in ipairs(player.Backpack:GetChildren()) do
                local backpackItemMatches = string.lower(backpackItem.Name) == string.lower(itemName)
                local backpackOldNameMatches = backpackItem:GetAttribute("oldName") ~= nil and 
                                              string.lower(backpackItem:GetAttribute("oldName")) == string.lower(itemName)
                
                if backpackItemMatches or backpackOldNameMatches then 
                    duplicate = true
                    break
                end
            end
            
            -- Clone and give the item if not a duplicate
            if not duplicate then
                local clone = item:Clone()
                clone.Parent = player.Backpack
            end
        end
    end
end

--[[
    Give items to a player based on their profile
    @param player - The player to give items to
    @param profile - The player's profile data
]]
function ItemHandler:GiveItems(player, profile)
    local character = player.Character or player.CharacterAdded:Wait()
    
    local function giveItems()
        for itemName, equipped in pairs(profile.Data.owned.items) do
            self:PlaceItem(player, itemName)
        end
    end
    
    -- Give items immediately
    giveItems()
    
    -- Set up character added connection to give items when the player respawns
    player.CharacterAdded:Connect(function()
        giveItems()
    end)
end

--[[
    Equip or unequip an item for a player
    @param player - The player to equip/unequip the item for
    @param item - The item to equip/unequip
    @param profile - The player's profile data
]]
function ItemHandler.equip(player, item, profile)
    local clone = ServerStorage.Items:FindFirstChild(item)
    
    -- Equip the item
    if clone and profile.Data.owned.items[item.Name] == false then
        profile.Data.owned.items[item] = true
        clone.Parent = player.Backpack
    end
    
    -- Unequip the item
    if profile.Data.owned.items[item] == true then
        profile.Data.owned.items[item.Name] = false
        local playerItem = player.Backpack:FindFirstChild(item) or player.Character:FindFirstChild(item)
        if playerItem then 
            playerItem:Destroy() 
        end
    end
end

--[[
    Handle player added event
    @param player - The player who was added
    @param profile - The player's profile data
    @param service - The UserContent service
]]
function ItemHandler.plrAdded(player, profile, service)
    task.wait(5)
    
    for _, item in ipairs(ServerStorage.Items:GetChildren()) do
        if profile.Data.owned.items[item.Name] == nil then
            for _, itemName in pairs(profile.Data.gamepasses) do
                local itemMatches = item.Name == itemName
                local oldNameMatches = item:GetAttribute("oldName") == itemName
                local gamepassNameMatches = item:GetAttribute("GamepassName") == itemName
                
                if itemMatches or oldNameMatches or gamepassNameMatches then
                    print("Giving " .. itemName)
                    service:GiveItem(player, itemName)
                end
            end
        end
    end
end

-- Return the module
return ItemHandler
