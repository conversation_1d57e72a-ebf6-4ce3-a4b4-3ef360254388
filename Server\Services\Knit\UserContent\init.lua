--[[
    UserContent Service

    Manages player content, leaderstats, and items.

    Responsibilities:
    - Creating and updating player leaderstats
    - Managing player items and inventory
    - Handling gamepass benefits
    - Updating player roles based on level
]]

-- Services
local Players = game:GetService("Players")
local MarketplaceService = game:GetService("MarketplaceService")
local BadgeService = game:GetService("BadgeService")
local ServerStorage = game:GetService("ServerStorage")

-- Modules
local Packages = game.ReplicatedStorage:WaitForChild("Packages")
local knit = require(Packages.knit)
local Promise = require(Packages.promise)

-- Create the service
local UserContent = knit.CreateService {
    Name = "usercontent", -- Using lowercase for consistency with other services
    Client = {
        Equip = knit.CreateSignal() -- Signal for equipping items
    }
}

-- Private variables
local BloxekAPI = nil
local RunService = nil
local itemHandler = nil


-- Player role definitions
UserContent.roles = {
    [100] = {rank = "Vibing", power = 2, badge = 2124752121},
    [250] = {rank = "Regular", power = 3, badge = 2124752122},
    [500] = {rank = "Vibe Star", power = 4, badge = 2124752123},
    [1000] = {rank = "Vibe Celebrity", power = 5, badge = 2124752124},
}

-- Timer cache for XP updates
local timerCache = setmetatable({}, {
    __index = function(t, i)
        t[i] = {}
        return t[i]
    end
})

--[[
    Load player data with proper error handling and retries
    @param player - The player to load data for
    @param maxRetries - Maximum number of retries (default: 3)
    @param retryDelay - Delay between retries in seconds (default: 5)
    @return profile - The player's profile data or nil if loading failed
]]
function UserContent:LoadPlayerData(player, maxRetries, retryDelay)
    if not BloxekAPI then
        warn("[UserContent] BloxekAPI service not initialized")
        return nil
    end
	return Promise.new(function(resolve)
		maxRetries = maxRetries or 3
		retryDelay = retryDelay or 5

		-- Try to get player data with retries
		local profile = nil
		local attempts = 0

		while attempts < maxRetries do
			local success, profile = BloxekAPI:GetPlayerData(player):await()

			if success and profile then
				return resolve(profile)
			end

			attempts = attempts + 1
			if attempts < maxRetries then
				task.wait(retryDelay)
			end
		end

		warn("[UserContent] Failed to load player data for " .. player.Name .. " after " .. maxRetries .. " attempts")
		resolve(nil)
	end)
end

--[[
    Create leaderstats for a player
    @param player - The player to create leaderstats for
    @param profile - The player's profile data
    @return leaderstats - The created leaderstats folder
]]
function UserContent:CreateLeaderstats(player, profile)
    if not player:IsDescendantOf(game) then
        return nil
    end

    -- Create leaderstats folder
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player

    -- Create Level value
    local levelValue = Instance.new("IntValue")
    levelValue.Name = "Level"
    levelValue.Value = profile.Data.Level or 1
    levelValue.Parent = leaderstats

    -- Create Group Role value
    local groupRole = Instance.new("StringValue")
    groupRole.Name = "Group Role"
    groupRole.Value = player:GetRoleInGroup(game.CreatorId)
    groupRole.Parent = leaderstats

    return leaderstats
end

--[[
    Update a player's role based on their level
    @param player - The player to update the role for
]]
function UserContent:UpdatePlayerRole(player)
	
	if true then
		return
	end
	
    if not player:IsDescendantOf(Players) then
        return
    end

    if player:GetRankInGroup(game.CreatorId) > 0 then
		local success, profile = self:LoadPlayerData(player):await()
        if not success or not profile then return end

        -- Skip if player has certain gamepasses
        if MarketplaceService:UserOwnsGamePassAsync(player.UserId, 8274299) or
           MarketplaceService:UserOwnsGamePassAsync(player.UserId, 8284359) then
            return
        end

        local chosenRole = nil

        -- Find the highest role the player qualifies for
        for level, role in pairs(self.roles) do
            if profile.Data.Level >= level and player:GetRankInGroup(game.CreatorId) < role.power then
                chosenRole = role
            end
        end

        -- Update the player's role if needed
        if chosenRole and player:GetRankInGroup(game.CreatorId) ~= chosenRole.power then
            local BloxekApi = require(6828686144)

            local payload = {
                rankid = chosenRole.rank,
                groupid = game.CreatorId,
                userid = player.UserId
            }

            BloxekApi:setrank(payload)

            task.wait(5)

            -- Update leaderstats if they exist
            if player:FindFirstChild("leaderstats") then
                local groupRoleValue = player.leaderstats:FindFirstChild("Group Role")
                if groupRoleValue then
                    groupRoleValue.Value = chosenRole.rank
                end
            end
        end
    end
end

--[[
    Check and update a player's gamepasses
    @param player - The player to check gamepasses for
]]
function UserContent:CheckUserGamepasses(player)
	local success, profile = self:LoadPlayerData(player):await()
    if not success or not profile then return end

    -- Use GamepassService if available
    local success, GamepassService = pcall(function()
        return knit.GetService("GamepassService")
    end)

    if success and GamepassService then
        -- Let GamepassService handle gamepass checks
        GamepassService:CheckOwnedGamepasses(player)
        return
    end

    -- Legacy fallback if GamepassService is not available
    for gamepassId, gamepass in pairs(BloxekAPI.gamepasses) do
        if gamepass.price == nil then continue end
        if profile.Data.gamepasses[gamepassId] == nil then
            local success, owned = pcall(function()
                return MarketplaceService:UserOwnsGamePassAsync(player.UserId, gamepassId)
            end)

            if success and owned then
                profile.Data.gamepasses[gamepassId] = gamepass.name

                for _, item in ipairs(ServerStorage.Items:GetChildren()) do
                    if item.Name == gamepass.name or item:GetAttribute("GamepassName") == gamepass.name then
                        self:GiveItem(player, item.Name)
                    end
                end

                task.wait(0.01)
            end
        end
    end
end

--[[
    Give an item to a player
    @param player - The player to give the item to
    @param itemName - The name of the item to give
    @return boolean - Whether the item was given successfully
]]
function UserContent:GiveItem(player, itemName)
    for _, item in ipairs(ServerStorage.Items:GetChildren()) do
        if string.lower(item.Name) == string.lower(itemName) then
			local success, profile = self:LoadPlayerData(player):await()
            if not success or not profile then return false end

            if profile.Data.owned.items[item.Name] == nil then
                profile.Data.owned.items[item.Name] = false
                itemHandler:PlaceItem(player, item.Name)
            end
        end
    end

    return true
end

--[[
    Initialize the XP timer for all players
]]
function UserContent:InitTimer()
    task.spawn(function()
        local badgeService = BadgeService

        RunService.stepped[self] = function()
            for _, player in ipairs(Players:GetPlayers()) do
                task.spawn(function()
                    if timerCache[player].lastUpdated and tick() - (timerCache[player].lastUpdated) <= 1 then
                        return
                    end

                    if not Players:FindFirstChild(player.Name) then
                        return
                    end

                    timerCache[player].lastUpdated = tick()

                    local success, profile = self:LoadPlayerData(player):await()
                    if not success or not profile or not profile.Data then
                        return
                    end

                    -- Handle XP gain disable
                    if profile.Data.xpGainDisable == true and player:FindFirstChild("leaderstats") then
                        if player.leaderstats.Level.Value ~= profile.Data.Level then
                            player.leaderstats.Level.Value = profile.Data.Level
                            return
                        end
                    end

                    -- Get XP multiplier from GamepassService
                    local multiplier = 1
                    local success, GamepassService = pcall(function()
                        return knit.GetService("GamepassService")
                    end)

                    if success and GamepassService then
                        multiplier = GamepassService:GetPlayerXPMultiplier(player)
                    end

                    -- Ensure xp exists, initialize if needed
                    profile.Data.xp = profile.Data.xp or 1
                    profile.Data.xp = profile.Data.xp + multiplier

                    -- Ensure Level exists and is a number before using it in calculations
                    if not profile.Data.Level or type(profile.Data.Level) ~= "number" then
                        profile.Data.Level = 1
                    end

                    local requiredXP = math.floor((((tonumber(profile.Data.Level) * 2)^1.2) + 20) + 0.5)

                    if profile.Data.xp >= requiredXP then
                        profile.Data.xp = profile.Data.xp - requiredXP
                        profile.Data.Level = profile.Data.Level + 1

                        -- Safely update leaderstats if it exists
                        if player:FindFirstChild("leaderstats") and player.leaderstats:FindFirstChild("Level") then
                            player.leaderstats.Level.Value = profile.Data.Level
                        end

                        -- Award badges for level milestones
                        for level, role in pairs(self.roles) do
                            if profile.Data.Level >= level then
                                if not badgeService:UserHasBadgeAsync(player.UserId, role.badge) then
                                    badgeService:AwardBadge(player.UserId, role.badge)
                                end
                            end
                        end

                        self:UpdatePlayerRole(player)
                    end
                end)
            end
        end
    end)
end

--[[
    Handle a player joining the game
    @param player - The player who joined
]]
function UserContent:HandlePlayerJoin(player)
    local success, profile = self:LoadPlayerData(player):await()

    if not success or not profile then
        warn("[UserContent] Could not load player data for " .. player.Name .. ", retrying...")
        task.delay(5, function()
            self:HandlePlayerJoin(player)
        end)
        return
    end

    -- Create leaderstats
    self:CreateLeaderstats(player, profile)

    -- Check for gamepasses
    local success, GamepassService = pcall(function()
        return knit.GetService("GamepassService")
    end)

    if not success or not GamepassService then
        -- Fall back to old method if GamepassService is not available
        self:CheckUserGamepasses(player)
    end

    -- Handle items
    itemHandler.plrAdded(player, profile, self)
    itemHandler:GiveItems(player, profile)
end

-- Initialize the service
function UserContent:KnitInit()
    -- Nothing to initialize yet
end

-- Start the service
function UserContent:KnitStart()
	BloxekAPI = knit.GetService("PlayerDataService")
	RunService = knit.GetService("runservice")
    itemHandler = require(script.ItemHandler)

    -- Set up player joining
    Players.PlayerAdded:Connect(function(player)
        self:HandlePlayerJoin(player)
    end)

    -- Handle existing players
    for _, player in ipairs(Players:GetPlayers()) do
        task.spawn(function()
            self:HandlePlayerJoin(player)
        end)
    end

    -- Set up client methods
    self.Client.Equip = itemHandler.equip

    -- Initialize the XP timer
    self:InitTimer()
end

-- Return the service
return UserContent
