--[[
    util.lua

    Utility functions for the server-side scripts.
    Provides common functionality used across multiple scripts.
]]

local HttpService = game:GetService("HttpService")

local util = {}

-- Logging function with support for formatting
function util.log(str, args, n)
    n = n or "Server"

    local message
    if args then
        message = string.format(str, table.unpack(args))
    else
        message = str
    end

    print("["..n.."]: " .. message)
    return message
end

-- Generate a UUID
function util.generateUUID()
    return HttpService:GenerateGUID(false)
end

-- Convert a JobId to a UUID format
function util.jobIdToUUID(jobId)
    -- If the jobId is already a UUID, return it
    if string.match(jobId, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
        return jobId
    end

    -- Create a deterministic UUID from the JobId
    local hash = HttpService:GenerateGUID(false)

    -- If we have a JobId, use it to create a more deterministic UUID
    if jobId and jobId ~= "" then
        -- Simple hashing to create a UUID-like string
        local hashBase = jobId

        -- Ensure it's at least 32 characters
        while #hashBase < 32 do
            hashBase = hashBase .. hashBase
        end

        -- Format as UUID (8-4-4-4-12)
        hash = string.sub(hashBase, 1, 8) .. "-" ..
               string.sub(hashBase, 9, 12) .. "-" ..
               string.sub(hashBase, 13, 16) .. "-" ..
               string.sub(hashBase, 17, 20) .. "-" ..
               string.sub(hashBase, 21, 32)
    end

    return hash
end

-- Deep copy a table
function util.deepCopy(original)
    local copy
    if type(original) == "table" then
        copy = {}
        for key, value in pairs(original) do
            copy[key] = util.deepCopy(value)
        end
    else
        copy = original
    end
    return copy
end

-- Merge two tables
function util.mergeTables(t1, t2)
    local result = util.deepCopy(t1)

    for key, value in pairs(t2) do
        if type(value) == "table" and type(result[key]) == "table" then
            result[key] = util.mergeTables(result[key], value)
        else
            result[key] = value
        end
    end

    return result
end

-- Alias for mergeTables for compatibility with tests
util.merge = util.mergeTables

-- Check if a value exists in a table
function util.tableContains(table, value)
    for _, v in pairs(table) do
        if v == value then
            return true
        end
    end
    return false
end

-- Get the keys of a table
function util.getKeys(table)
    local keys = {}
    for key, _ in pairs(table) do
        table.insert(keys, key)
    end
    return keys
end

-- Format time in seconds to a human-readable string
function util.formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local hours = math.floor(minutes / 60)
    local days = math.floor(hours / 24)

    seconds = seconds % 60
    minutes = minutes % 60
    hours = hours % 24

    if days > 0 then
        return string.format("%dd %dh %dm %ds", days, hours, minutes, seconds)
    elseif hours > 0 then
        return string.format("%dh %dm %ds", hours, minutes, seconds)
    elseif minutes > 0 then
        return string.format("%dm %ds", minutes, seconds)
    else
        return string.format("%ds", seconds)
    end
end

return util