--[[
    Auth Module

    Handles authentication with the backend server.
    Uses time-based hash authentication with a shared secret key.
]]

-- Module table
local Auth = {}

-- Dependencies
local HTTP = nil

-- Default configuration
Auth.Config = {
    BaseURL = "https://tvc.drandex.org",
    SharedSecretKey = "3d95283f2d38592f745f138c3b3f656481bd9e77a3aef6bc491db014c35bf7ae", -- This should match the server's SHARED_SECRET_KEY
    TimeWindow = 300, -- 5 minutes, should match the server's TIME_WINDOW
    Endpoints = {},
    Debug = true
}

-- Initialize the Auth module
function Auth:Initialize(config)
    -- Get HTTP module reference
    HTTP = require(script.Parent.http)

    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            if key == "Endpoints" and type(value) == "table" then
                for endpointKey, endpointValue in pairs(value) do
                    self.Config.Endpoints[endpointKey] = endpointValue
                end
            else
                self.Config[key] = value
            end
        end
    end

    return self
end

-- Generate a time-based hash
function Auth:GenerateTimeBasedHash(timestamp)
    -- Get the HttpService for JSON encoding/decoding
    local HttpService = game:GetService("HttpService")

    -- Convert timestamp to string
    local message = tostring(timestamp)

    -- Check if we're using the default shared secret key
    if self.Config.SharedSecretKey == "your_secure_shared_key_here" then
        print("[Auth] WARNING: Using default shared secret key. This should be changed in production.")
    end

    -- Since Roblox doesn't have a built-in HMAC function, we'll use a simple approach
    -- We'll create a hash that only contains valid characters for HTTP headers

    -- Create a simple hash by combining the secret key, timestamp, and a random value
    local randomValue = HttpService:GenerateGUID(false)

    -- Create a more secure hash by using a specific format
    -- Format: [first 4 chars of key]_[timestamp]_[last 4 chars of key]_[random value]
    local keyPrefix = string.sub(self.Config.SharedSecretKey, 1, 4)
    local keySuffix = string.sub(self.Config.SharedSecretKey, -4)

    -- If the key is too short, use the whole key
    if #self.Config.SharedSecretKey < 8 then
        keyPrefix = self.Config.SharedSecretKey
        keySuffix = self.Config.SharedSecretKey
    end

    local combinedString = keyPrefix .. "_" .. message .. "_" .. keySuffix .. "_" .. randomValue

    -- Create a hash by replacing any potentially problematic characters
    -- This ensures the token only contains valid characters for HTTP headers
    local hash = ""
    for i = 1, #combinedString do
        local char = string.sub(combinedString, i, i)
        -- Only include alphanumeric characters and some safe symbols
        if string.match(char, "[A-Za-z0-9_%-]") then
            hash = hash .. char
        else
            -- Replace other characters with their byte value
            hash = hash .. string.format("_%d_", string.byte(char))
        end
    end

    -- Trim to a reasonable length (64 characters)
    if #hash > 64 then
        hash = string.sub(hash, 1, 64)
    elseif #hash < 64 then
        -- Pad with underscores if too short
        hash = hash .. string.rep("_", 64 - #hash)
    end

    if self.Config.Debug then
        print("[Auth] Generated hash for timestamp " .. message .. ": " .. hash)
        print("[Auth] Hash length: " .. #hash)
    end

    return hash
end

-- Get authentication headers for API requests
function Auth:GetAuthHeaders()
    return HTTP:Promise(function(resolve, reject)
        -- Get current timestamp
        local timestamp = os.time()

        -- Generate the authentication token using the shared secret key
        local authToken = self:GenerateTimeBasedHash(timestamp)

        -- Debug logging
        if self.Config.Debug then
            print("[Auth] Current timestamp: " .. tostring(timestamp))
            print("[Auth] Generated auth token: " .. authToken)
        end

        -- Create headers with the token and timestamp
        local headers = {
            ["Authorization"] = "Bearer " .. authToken,
            ["X-Timestamp"] = tostring(timestamp)
        }

        -- Debug logging
        if self.Config.Debug then
            print("[Auth] Final headers: " .. game:GetService("HttpService"):JSONEncode(headers))
        end

        resolve(headers)
    end)
end

-- Return the module
return Auth
