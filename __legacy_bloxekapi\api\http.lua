--[[
    HTTP Module

    Handles HTTP requests to the backend server.
    Provides a Promise-based interface for making requests.
]]

local HttpService = game:GetService("HttpService")

-- Module table
local HTTP = {}

-- Default configuration
HTTP.Config = {
    BaseURL = "https://tvc.drandex.org",
    Timeout = 10,
    RetryCount = 3,
    RetryDelay = 1,
    UseAuth = true,
    APIKey = nil,
    Debug = false,
    ServerId = game.JobId, -- Use Roblox's built-in JobId as the server instance identifier
    UUIDServerId = nil -- This will be set to a UUID version of the JobId when needed
}

-- Promise implementation
local Promise = {}
Promise.__index = Promise

function Promise.new(callback)
    local self = setmetatable({
        _status = "pending",
        _value = nil,
        _reason = nil,
        _thenCallbacks = {},
        _catchCallbacks = {}
    }, Promise)

    local resolve = function(value)
        if self._status == "pending" then
            self._status = "fulfilled"
            self._value = value

            for _, callback in ipairs(self._thenCallbacks) do
                task.spawn(function()
                    callback(value)
                end)
            end
        end
    end

    local reject = function(reason)
        if self._status == "pending" then
            self._status = "rejected"
            self._reason = reason

            for _, callback in ipairs(self._catchCallbacks) do
                task.spawn(function()
                    callback(reason)
                end)
            end
        end
    end

    task.spawn(function()
        local success, result = pcall(callback, resolve, reject)
        if not success and self._status == "pending" then
            reject(result)
        end
    end)

    return self
end

function Promise:andThen(callback)
    if self._status == "fulfilled" then
        task.spawn(function()
            callback(self._value)
        end)
    elseif self._status == "pending" then
        table.insert(self._thenCallbacks, callback)
    end

    return self
end

function Promise:catch(callback)
    if self._status == "rejected" then
        task.spawn(function()
            callback(self._reason)
        end)
    elseif self._status == "pending" then
        table.insert(self._catchCallbacks, callback)
    end

    return self
end

-- Initialize the HTTP module
function HTTP:Initialize(config)
    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            self.Config[key] = value
        end
    end

    return self
end

-- Create a Promise
function HTTP:Promise(callback)
    return Promise.new(callback)
end

-- Make an HTTP request
function HTTP:Request(method, endpoint, body, headers)
    return Promise.new(function(resolve, reject)
        -- Build URL
        local url = self.Config.BaseURL .. endpoint

        -- Build headers
        headers = headers or {}
        headers["Content-Type"] = headers["Content-Type"] or "application/json"

        -- Add server ID to headers if not already provided
        if not headers["X-Server-ID"] then
            -- Ensure the JobId is a valid UUID
            local success, result = pcall(function()
                local Chatlog = require(script.Parent.Parent.chatlog)
                return Chatlog:JobIdToUUID(self.Config.ServerId)
            end)

            if success and result then
                -- Use the UUID-formatted JobId
                headers["X-Server-ID"] = result
                print("[HTTP] Using UUID-formatted JobId as server ID: " .. result)
            else
                -- Fallback to using the JobId directly (though this might cause issues)
                headers["X-Server-ID"] = self.Config.ServerId
                print("[HTTP] Failed to format JobId as UUID, using raw JobId: " .. self.Config.ServerId)
            end
        end

        -- Add authentication if enabled and not already provided
        if self.Config.UseAuth and self.Config.APIKey and not headers["Authorization"] then
            headers["Authorization"] = "Bearer " .. self.Config.APIKey
        end

        -- Build request options
        local options = {
            Url = url,
            Method = method,
            Headers = headers,
            Body = body and HttpService:JSONEncode(body) or nil
        }

        -- Debug logging
        if self.Config.Debug then
            print("[HTTP] Request:", method, url)
            print("[HTTP] Headers:", HttpService:JSONEncode(headers))
            if body then
                local bodyStr = ""
                if type(body) == "string" then
                    bodyStr = body
                else
                    -- Try to encode the body as JSON, but handle any errors
                    local success, result = pcall(function()
                        return HttpService:JSONEncode(body)
                    end)

                    if success then
                        bodyStr = result
                    else
                        bodyStr = "Failed to encode body: " .. tostring(result)
                    end
                end

                -- Truncate very long bodies in logs
                if #bodyStr > 1000 then
                    print("[HTTP] Body (truncated):", string.sub(bodyStr, 1, 1000) .. "...")
                else
                    print("[HTTP] Body:", bodyStr)
                end
            end
        end

        -- Always log requests for debugging during development
        print("[HTTP] Request:", method, url)
        print("[HTTP] Headers:", HttpService:JSONEncode(headers))
        if body then
            print("[HTTP] Body:", type(body) == "string" and body or HttpService:JSONEncode(body))
        end

        -- Make the request with retry logic
        local function makeRequest(retryCount)
            local success, result = pcall(function()
                return HttpService:RequestAsync(options)
            end)

            if success then
                -- Parse response
                local response = result

                -- Debug logging
                if self.Config.Debug then
                    print("[HTTP] Response:", response.StatusCode)
                    print("[HTTP] Body:", response.Body)
                end

                -- Always log responses for debugging during development
                print("[HTTP] Response:", response.StatusCode)
                print("[HTTP] Body:", response.Body)

                -- Log the endpoint for context
                print("[HTTP] Endpoint:", method, url)

                -- Check status code
                if response.StatusCode >= 200 and response.StatusCode < 300 then
                    -- Success
                    local responseBody = nil

                    -- Try to parse JSON response
                    if response.Body and response.Body ~= "" then
                        local parseSuccess, parsedBody = pcall(function()
                            return HttpService:JSONDecode(response.Body)
                        end)

                        if parseSuccess then
                            responseBody = parsedBody
                        else
                            responseBody = response.Body
                        end
                    end

                    resolve(responseBody)
                else
                    -- Error
                    if retryCount > 0 and response.StatusCode >= 500 then
                        -- Retry server errors
                        wait(self.Config.RetryDelay)
                        makeRequest(retryCount - 1)
                    else
                        -- Client error or out of retries
                        reject("HTTP Error: " .. response.StatusCode .. " - " .. (response.Body or ""))
                    end
                end
            else
                -- Request failed
                if retryCount > 0 then
                    -- Retry
                    wait(self.Config.RetryDelay)
                    makeRequest(retryCount - 1)
                else
                    -- Out of retries
                    reject("Request failed: " .. tostring(result))
                end
            end
        end

        -- Start the request
        makeRequest(self.Config.RetryCount)
    end)
end

-- Shorthand methods
function HTTP:Get(endpoint, headers)
    return self:Request("GET", endpoint, nil, headers)
end

function HTTP:Post(endpoint, body, headers)
    return self:Request("POST", endpoint, body, headers)
end

function HTTP:Put(endpoint, body, headers)
    return self:Request("PUT", endpoint, body, headers)
end

function HTTP:Delete(endpoint, headers)
    return self:Request("DELETE", endpoint, nil, headers)
end

-- Return the module
return HTTP
