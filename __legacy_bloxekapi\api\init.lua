--[[
    API Module (rlua)

    Main API module for interacting with the backend server.
    Provides a clean interface for making HTTP requests to the backend API.
]]

local HttpService = game:GetService("HttpService")

-- Import sub-modules
local HTTP = require(script.http)
local PlayerData = require(script.playerdata)
local Auth = require(script.auth)

-- Module table
local API = {}

-- Default configuration
API.Config = {
    BaseURL = "https://tvc.drandex.org",  -- Default to localhost for development
    Timeout = 10,                       -- Default timeout in seconds
    RetryCount = 3,                     -- Number of retries for failed requests
    RetryDelay = 1,                     -- Delay between retries in seconds
    UseAuth = true,                     -- Whether to use authentication
    APIKey = nil,                       -- API key for authentication
    Debug = false                       -- Debug mode
}

-- Sub-modules
API.HTTP = HTTP
API.PlayerData = PlayerData
API.Auth = Auth

-- Initialize the API module
function API:Initialize(config)
    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            self.Config[key] = value
        end
    end

    -- Initialize sub-modules
    HTTP:Initialize(self.Config)
    PlayerData:Initialize(self.Config)
    Auth:Initialize(self.Config)

    return self
end

-- Get the current configuration
function API:GetConfig()
    return self.Config
end

-- Update the configuration
function API:UpdateConfig(config)
    for key, value in pairs(config) do
        self.Config[key] = value
    end

    -- Update sub-modules
    HTTP:Initialize(self.Config)
    PlayerData:Initialize(self.Config)
    Auth:Initialize(self.Config)

    return self.Config
end

-- Create a Promise
function API:Promise(callback)
    return HTTP:Promise(callback)
end

-- Return the module
return API
