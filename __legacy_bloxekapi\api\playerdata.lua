--[[
    PlayerData API Module

    Handles player data API endpoints.
    Provides methods for getting and updating player data.
]]

-- Module table
local PlayerData = {}

-- Dependencies
local HTTP = nil

-- Default configuration
PlayerData.Config = {
    BaseURL = "https://tvc.drandex.org",
    Endpoints = {
        GetPlayerData = "/playerdata/roblox/%s",  -- %s will be replaced with userId
        UpdatePlayerData = "/playerdata/roblox/%s",
        CreatePlayerData = "/playerdata/roblox",
        ReleasePlayerData = "/playerdata/roblox/%s/release"  -- %s will be replaced with userId
    }
}

-- Server ID (using Roblox's built-in JobId as the server instance identifier)
PlayerData.ServerId = game.JobId

-- Function to get a UUID version of the JobId
function PlayerData:GetUUIDServerId()
    -- Try to get the Chatlog module to convert the JobId to a UUID
    local success, result = pcall(function()
        local Chatlog = require(script.Parent.Parent.chatlog)
        return Chatlog:JobIdToUUID(self.ServerId)
    end)

    if success and result then
        return result
    else
        return self.ServerId
    end
end

-- Initialize the PlayerData module
function PlayerData:Initialize(config)
    -- Get HTTP module reference
    HTTP = require(script.Parent.http)

    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            if key == "Endpoints" and type(value) == "table" then
                for endpointKey, endpointValue in pairs(value) do
                    self.Config.Endpoints[endpointKey] = endpointValue
                end
            else
                self.Config[key] = value
            end
        end
    end

    return self
end

-- Get player data by userId
function PlayerData:GetPlayerData(userId, retryCount, forceParam)
    -- Set default retry count if not provided
    retryCount = retryCount or 0
    local maxRetries = 5
    local retryDelay = 1 -- seconds
    local useForce = forceParam or false

    -- Format endpoint with userId
    local endpoint = string.format(self.Config.Endpoints.GetPlayerData, userId)

    -- Add force parameter if specified
    if useForce then
        print("[PlayerData] Using force=true parameter")
        if not endpoint:find("?") then
            endpoint = endpoint .. "?force=true"
        else
            endpoint = endpoint .. "&force=true"
        end
        print("[PlayerData] Force endpoint: " .. endpoint)
    end

    -- Create a new promise to handle the entire operation
    return HTTP:Promise(function(resolve, reject)
        -- Get authentication headers
        local Auth = require(script.Parent.auth)
        Auth:GetAuthHeaders()
            :andThen(function(headers)
                -- Add server ID to headers (using UUID format)
                headers["X-Server-ID"] = self:GetUUIDServerId()

                -- Make request with authentication headers
                HTTP:Get(endpoint, headers)
                    :andThen(function(response)
                        -- Process response
                        print("[PlayerData] Raw response received:", game:GetService("HttpService"):JSONEncode(response))

                        -- Check if response is valid
                        if response then
                            -- Check if profile exists and is not empty
                            if response.profile and next(response.profile) ~= nil then
                                print("[PlayerData] Valid profile found for user " .. userId)
                                resolve(response)
                            elseif response.profile and next(response.profile) == nil then
                                -- Profile exists but is empty, create a default profile
                                print("[PlayerData] Empty profile received for user " .. userId .. ", creating default profile")

                                -- Create a default profile
                                local defaultProfile = self:CreateDefaultProfile()

                                -- Merge with the existing response
                                response.profile = defaultProfile

                                -- Log the modified response
                                print("[PlayerData] Created default profile for user " .. userId)

                                -- Return the modified response
                                resolve(response)
                            elseif response.data then
                                -- Some APIs return data in a 'data' field
                                print("[PlayerData] Data found in 'data' field for user " .. userId)
                                resolve(response.data)
                            else
                                -- Response exists but doesn't have profile or data
                                print("[PlayerData] Response missing profile and data for user " .. userId .. ", creating default response")

                                -- Create a default response
                                local defaultResponse = {
                                    id = response.id or 0,
                                    userid = userId,
                                    loaded = self:GetUUIDServerId(),
                                    last_joined = response.last_joined or os.date("!%Y-%m-%dT%H:%M:%S.000Z"),
                                    profile = self:CreateDefaultProfile()
                                }

                                -- Log the default response
                                print("[PlayerData] Created default response for user " .. userId)

                                -- Return the default response
                                resolve(defaultResponse)
                            end
                        else
                            -- No response, create a completely default one
                            print("[PlayerData] No response received for user " .. userId .. ", creating default response")

                            -- Create a default response
                            local defaultResponse = {
                                id = 0,
                                userid = userId,
                                loaded = self:GetUUIDServerId(),
                                last_joined = os.date("!%Y-%m-%dT%H:%M:%S.000Z"),
                                profile = self:CreateDefaultProfile()
                            }

                            -- Log the default response
                            print("[PlayerData] Created default response for user " .. userId)

                            -- Return the default response
                            resolve(defaultResponse)
                        end
                    end)
                    :catch(function(err)
                        -- Check if the error is a 423 Locked status
                        if tostring(err):find("423") then
                            print("[PlayerData] Profile is locked by another server for user " .. userId)

                            -- Check if we should retry
                            if retryCount < maxRetries then
                                print("[PlayerData] Retrying in " .. retryDelay .. " seconds (attempt " .. (retryCount + 1) .. "/" .. maxRetries .. ")")

                                -- Wait before retrying
                                wait(retryDelay)

                                -- Determine if we should use force on this retry
                                local nextUseForce = (retryCount == maxRetries - 1) or useForce

                                -- Retry getting the player data with the force parameter if needed
                                self:GetPlayerData(userId, retryCount + 1, nextUseForce)
                                    :andThen(function(retryResponse)
                                        resolve(retryResponse)
                                    end)
                                    :catch(function(retryErr)
                                        reject(retryErr)
                                    end)
                            else
                                print("[PlayerData] Max retries reached, giving up on loading profile for user " .. userId)
                                reject("Profile is locked and max retries reached: " .. tostring(err))
                            end
                        else
                            print("[PlayerData] Error getting player data: " .. tostring(err))
                            reject(err)
                        end
                    end)
            end)
            :catch(function(err)
                print("[PlayerData] Failed to get auth headers: " .. tostring(err))
                reject("Failed to get auth headers: " .. tostring(err))
            end)
    end)
end

-- Create a default profile
function PlayerData:CreateDefaultProfile()
    return {
        ["Data"] = {
            ["xp"] = 1,
            ["vip"] = false,
            ["Level"] = 1,
            ["owned"] = {
                ["tags"] = {},
                ["items"] = {},
                ["badges"] = {},
                ["rotech"] = {},
                ["animations"] = {},
                ["gamepasses"] = {},
                ["collectibles"] = {["menofsnow"] = {}, ["vibecoins"] = {}}
            },
            ["badges"] = {},
            ["metadata"] = {
                ["LastJoin"] = 0,
                ["ChatsSent"] = 0,
                ["RobuxSpent"] = 0,
                ["TimePlayed"] = 0,
                ["SessionLoadCount"] = 0,
                ["__migrated"] = 0
            },
            ["settings"] = {
                ["fov"] = 70,
                ["plrs"] = false,
                ["tags"] = false,
                ["volume"] = 50,
                ["rainbow"] = true,
                ["ovhcolor"] = "1, 1, 1",
                ["sfxVolume"] = 100,
                ["potatoMode"] = false,
                ["musicVolume"] = 100,
                ["lowDetailMode"] = false
            },
            ["gamepasses"] = {},
            ["hasTransferredToDb"] = false,
            ["hasTransferredTimeData"] = false,
            ["__migrated"] = 0
        },
        ["UserIds"] = {},
        ["MetaData"] = {
            ["MetaTags"] = {},
            ["LastUpdate"] = 0,
            ["SessionLoadCount"] = 0,
            ["ProfileCreateTime"] = 0
        },
        ["WasCorrupted"] = false,
        ["GlobalUpdates"] = {0, {}},
        ["RobloxMetaData"] = {}
    }
end

-- Update player data
function PlayerData:UpdatePlayerData(userId, data)
    -- Format endpoint with userId
    local endpoint = string.format(self.Config.Endpoints.UpdatePlayerData, userId)

    -- Create a new promise to handle the entire operation
    return HTTP:Promise(function(resolve, reject)
        -- Get authentication headers
        local Auth = require(script.Parent.auth)
        Auth:GetAuthHeaders()
            :andThen(function(headers)
                -- Make request with authentication headers
                HTTP:Put(endpoint, data, headers)
                    :andThen(function(response)
                        -- Process response
                        resolve(response)
                    end)
                    :catch(function(err)
                        reject(err)
                    end)
            end)
            :catch(function(err)
                reject("Failed to get auth headers: " .. tostring(err))
            end)
    end)
end

-- Create player data
function PlayerData:CreatePlayerData(userId, data)
    -- Prepare request body
    local body = {
        userid = userId,
        data = data
    }

    -- Create a new promise to handle the entire operation
    return HTTP:Promise(function(resolve, reject)
        -- Get authentication headers
        local Auth = require(script.Parent.auth)
        Auth:GetAuthHeaders()
            :andThen(function(headers)
                -- Make request with authentication headers
                HTTP:Post(self.Config.Endpoints.CreatePlayerData, body, headers)
                    :andThen(function(response)
                        -- Process response
                        resolve(response)
                    end)
                    :catch(function(err)
                        reject(err)
                    end)
            end)
            :catch(function(err)
                reject("Failed to get auth headers: " .. tostring(err))
            end)
    end)
end

-- Release player data (clear the loaded field)
function PlayerData:ReleasePlayerData(userId)
    -- Format endpoint with userId
    local endpoint = string.format(self.Config.Endpoints.ReleasePlayerData, userId)

    -- Create a new promise to handle the entire operation
    return HTTP:Promise(function(resolve, reject)
        -- Get authentication headers
        local Auth = require(script.Parent.auth)
        Auth:GetAuthHeaders()
            :andThen(function(headers)
                -- Add server ID to headers (using UUID format)
                headers["X-Server-ID"] = self:GetUUIDServerId()

                -- Make request with authentication headers
                HTTP:Post(endpoint, {}, headers)
                    :andThen(function(response)
                        -- Process response
                        print("[PlayerData] Successfully released profile for user " .. userId)
                        resolve(response)
                    end)
                    :catch(function(err)
                        print("[PlayerData] Error releasing player data: " .. tostring(err))
                        reject(err)
                    end)
            end)
            :catch(function(err)
                print("[PlayerData] Failed to get auth headers: " .. tostring(err))
                reject("Failed to get auth headers: " .. tostring(err))
            end)
    end)
end

-- Return the module
return PlayerData
