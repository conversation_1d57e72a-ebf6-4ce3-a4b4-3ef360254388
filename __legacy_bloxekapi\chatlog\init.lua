--[[
    Chatl<PERSON> Module

    Monitors and collects chat messages from players and sends them to the backend server.
    Features:
    - Chat message monitoring
    - Batch collection of messages
    - Periodic sending to backend
    - Fallback mechanism for failed sends
]]

local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Import modules
local HTTP = nil
local Auth = nil
local Logger = nil

-- Module table
local Chatlog = {}

-- Default configuration
Chatlog.Config = {
    BatchSize = 10,           -- Number of messages to batch before sending
    SendInterval = 60,        -- Seconds between sends if batch size not reached
    MaxRetries = 3,           -- Maximum number of retries for failed sends
    RetryDelay = 5,           -- Seconds between retries
    Endpoint = "/chatlogs/roblox",   -- API endpoint for chat logs
    Debug = true,             -- Debug mode
    CacheClearInterval = 3600 -- Clear the sent messages cache every hour (3600 seconds)
}

-- Message queue
Chatlog.MessageQueue = {}

-- Initialize the Chatlog module
function Chatlog:Initialize(config)
    -- Get module references
    HTTP = require(script.Parent.api.http)
    Auth = require(script.Parent.api.auth)
    Logger = require(script.Parent.utils.logger)

    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            self.Config[key] = value
        end
    end

    -- Initialize sent messages cache
    self.SentMessages = {}
    self.SentMessagesMaxSize = config and config.SentMessagesMaxSize or 1000

    -- Track the last time we cleared the sent messages cache
    self.LastCacheClearTime = os.time()

    -- Initialize message content map for duplicate detection
    self.MessageContentMap = {}

    Logger:Info("Initializing Chatlog module")

    -- Set up chat monitoring
    self:SetupChatMonitoring()

    -- Set up periodic sending
    self:SetupPeriodicSending()

    -- Set up shutdown handler
    self:SetupShutdownHandler()

    Logger:Info("Chatlog module initialized successfully")

    return self
end

-- Set up chat monitoring
function Chatlog:SetupChatMonitoring()
    -- Monitor existing players
    for _, player in pairs(Players:GetPlayers()) do
        self:MonitorPlayerChat(player)
    end

    -- Monitor new players
    Players.PlayerAdded:Connect(function(player)
        self:MonitorPlayerChat(player)
    end)
end

-- Monitor a player's chat
function Chatlog:MonitorPlayerChat(player)
    -- Connect to the player's chat event
    player.Chatted:Connect(function(message)
        self:AddChatMessage(player, message)
    end)
end

-- Function to create a deterministic UUID from a string
function Chatlog:CreateDeterministicUUID(inputStr)
    -- Use a simple but deterministic hashing algorithm
    local function simpleHash(str)
        local hash = 5381  -- Initial value (prime number)
        for i = 1, #str do
            -- DJB2 hash algorithm - very simple but effective for our needs
            hash = ((hash * 33) + string.byte(str, i)) % 0x100000000
        end
        return hash
    end

    -- Create multiple hashes from the input string to ensure we have enough bits
    local hash1 = simpleHash(inputStr)
    local hash2 = simpleHash(inputStr .. "salt1")
    local hash3 = simpleHash(inputStr .. "salt2")
    local hash4 = simpleHash(inputStr .. "salt3")

    -- Format the hashes as hex strings
    local hex1 = string.format("%08x", hash1)
    local hex2 = string.format("%08x", hash2)
    local hex3 = string.format("%08x", hash3)
    local hex4 = string.format("%08x", hash4)

    -- Combine the hashes to create a UUID-like string
    -- Format: 8-4-4-4-12 characters
    local uuid = string.sub(hex1, 1, 8) .. "-" ..
                 string.sub(hex2, 1, 4) .. "-" ..
                 -- Make it a valid v4 UUID by setting specific bits
                 "4" .. string.sub(hex2, 5, 7) .. "-" ..
                 -- Set the variant bits
                 "8" .. string.sub(hex3, 1, 3) .. "-" ..
                 string.sub(hex3, 4, 7) .. string.sub(hex4, 1, 8)

    -- Log the input and output for debugging
    if self.Config and self.Config.Debug then
        local Logger = require(script.Parent.utils.logger)
        Logger:Debug("CreateDeterministicUUID input: " .. inputStr)
        Logger:Debug("CreateDeterministicUUID output: " .. uuid)
    end

    return uuid
end

-- Function to ensure the JobId is a valid UUID
function Chatlog:JobIdToUUID(jobId)
    -- Check if the JobId is already in UUID format (8-4-4-4-12)
    if string.match(jobId, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
        return jobId
    end

    -- If not in UUID format, convert it to a valid UUID
    -- First, remove any non-hex characters
    local cleanJobId = string.gsub(jobId, "[^%x]", "")

    -- Ensure we have at least 32 hex characters
    while #cleanJobId < 32 do
        cleanJobId = cleanJobId .. "0"
    end

    -- Truncate if longer than 32
    if #cleanJobId > 32 then
        cleanJobId = string.sub(cleanJobId, 1, 32)
    end

    -- Format as UUID (8-4-4-4-12)
    local uuid = string.sub(cleanJobId, 1, 8) .. "-" ..
                 string.sub(cleanJobId, 9, 12) .. "-" ..
                 -- Make it a valid v4 UUID by setting specific bits
                 "4" .. string.sub(cleanJobId, 13, 15) .. "-" ..
                 -- Set the variant bits
                 "8" .. string.sub(cleanJobId, 17, 19) .. "-" ..
                 string.sub(cleanJobId, 21, 32)

    -- Log the conversion for debugging
    if self.Config and self.Config.Debug then
        local Logger = require(script.Parent.utils.logger)
        Logger:Info("Converted JobId to UUID: " .. jobId .. " -> " .. uuid)
    end

    return uuid
end

-- Add a chat message to the queue
function Chatlog:AddChatMessage(player, message)
    -- Get current timestamp
    local timestamp = os.time()
    local sender_userid = tostring(player.UserId)

    -- Format timestamp consistently for both the hash and the message object
    local formattedTimestamp = os.date("!%Y-%m-%dT%H:%M:%SZ", timestamp)

    -- Create a deterministic content string using the formatted timestamp
    -- This ensures the message_id will be consistent even if the timestamp is formatted differently
    local contentToHash = sender_userid .. ":" .. message .. ":" .. formattedTimestamp

    -- Log the content string for debugging
    if self.Config and self.Config.Debug then
        local Logger = require(script.Parent.utils.logger)
        Logger:Debug("Content string for UUID generation: " .. contentToHash)
    end

    -- Generate a deterministic UUID based on the content
    -- This is used as the server_id field in the database, but it's actually a message identifier
    -- We keep using server_id as the field name for backward compatibility with the backend
    local server_id = game.JobId

    -- Note: The actual server ID (JobId) is sent in the X-Server-ID header, not in the message

    -- Generate a client-side UUID that will be unique for each message
    -- This will be used as a primary key in the database to prevent duplicates
    local client_uuid = HttpService:GenerateGUID(false)


    -- Verify the UUID format is correct
    --if not string.match(client_uuid, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
    --    Logger:Warning("Generated client_uuid has invalid format: " .. client_uuid)
    --    -- Attempt to fix it by manually formatting
    --    local rawUUID = string.gsub(client_uuid, "-", "") -- Remove any existing hyphens
    --    if #rawUUID >= 32 then
    --        client_uuid = string.sub(rawUUID, 1, 8) .. "-" ..
    --                     string.sub(rawUUID, 9, 12) .. "-" ..
    --                     string.sub(rawUUID, 13, 16) .. "-" ..
    --                     string.sub(rawUUID, 17, 20) .. "-" ..
    --                     string.sub(rawUUID, 21, 32)
    --        Logger:Info("Fixed client_uuid format: " .. client_uuid)
    --    end
    --end

    -- Store the original message content and timestamp in our sent messages cache
    -- This will help us identify duplicates even with the UUID format
    local messageKey = sender_userid .. ":" .. message .. ":" .. timestamp
    self.MessageContentMap = self.MessageContentMap or {}
	self.MessageContentMap[messageKey] = client_uuid

    -- Log the UUIDs for debugging
    if self.Config.Debug then
		Logger:Debug("Generated deterministic message_id: " .. client_uuid)
        Logger:Debug("Using server_id (JobId): " .. server_id)
        Logger:Debug("Generated client_uuid: " .. client_uuid)
        Logger:Debug("UUID format check: " .. tostring(string.match(client_uuid, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") ~= nil))
    end

    -- Create a chat message object
    -- Use the same formatted timestamp that was used for the hash
    local chatMessage = {
        message = message,
        timestamp = formattedTimestamp, -- Use the same formatted timestamp as used for the hash
        sender_userid = sender_userid,
        server_id = server_id, -- This is the actual server ID (JobId)
        message_id = client_uuid, -- This is the deterministic message identifier
        client_uuid = client_uuid -- Add the client-side UUID
    }

    -- Add to queue
    table.insert(self.MessageQueue, chatMessage)

    -- Log
    if self.Config.Debug then
        Logger:Debug("Added chat message to queue: " .. player.Name .. ": " .. message)
    end

    -- Send if batch size reached
    if #self.MessageQueue >= self.Config.BatchSize then
        self:SendChatLogs()
    end
end

-- Set up periodic sending
function Chatlog:SetupPeriodicSending()
    -- Create a periodic task
    spawn(function()
        while true do
            wait(self.Config.SendInterval)

            -- Send if there are messages in the queue
            if #self.MessageQueue > 0 then
                self:SendChatLogs()
            end

            -- Periodically check and clear the cache if needed
            local currentTime = os.time()
            local timeSinceLastClear = currentTime - (self.LastCacheClearTime or 0)

            if timeSinceLastClear > self.Config.CacheClearInterval then
                local sentMessagesCount = 0
                for _ in pairs(self.SentMessages) do
                    sentMessagesCount = sentMessagesCount + 1
                end

                Logger:Debug("Periodic cache check: " .. sentMessagesCount .. " entries, " ..
                             timeSinceLastClear .. " seconds since last clear")

                if sentMessagesCount > 0 then
                    Logger:Info("Clearing sent messages cache during periodic check (size: " ..
                                sentMessagesCount .. ", time since last clear: " .. timeSinceLastClear .. " seconds)")
                    self.SentMessages = {}
                    self.MessageContentMap = {}
                    self.LastCacheClearTime = currentTime
                end
            end
        end
    end)
end

-- Send chat logs to the backend
function Chatlog:SendChatLogs()
    -- Check if there are messages to send
    if #self.MessageQueue == 0 then
        return
    end

    -- Get messages to send
    local messagesToSend = {}
    local messagesToSendCount = math.min(#self.MessageQueue, self.Config.BatchSize)
    local messagesToRemoveIndices = {}

    for i = 1, messagesToSendCount do
        local message = self.MessageQueue[i]

        -- Skip if index is out of bounds (could happen if messages were removed)
        if not message then
            continue
        end

        -- Validate server_id before sending
        if not message.server_id or message.server_id == "" then
            -- Generate a deterministic UUID if server_id is missing or empty
            local contentToHash = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp
            message.server_id = self:CreateDeterministicUUID(contentToHash)
            Logger:Debug("Generated deterministic server_id for message: " .. message.server_id)
        end

        -- Always verify the server_id is in the correct format
        if not string.match(message.server_id, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
            Logger:Warning("server_id has invalid format, regenerating: " .. message.server_id)
            local contentToHash = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp
            message.server_id = self:CreateDeterministicUUID(contentToHash)
            Logger:Debug("Regenerated server_id: " .. message.server_id)
        end

        -- Ensure timestamp is in the correct format
        if type(message.timestamp) == "number" then
            message.timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ", message.timestamp)
            Logger:Debug("Converted numeric timestamp to formatted string: " .. message.timestamp)
        end

        -- Create a message key for duplicate detection
        local messageKey = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp

        -- Check if this message has already been sent successfully
        if not self.SentMessages[message.server_id] and not self.SentMessages[messageKey] then
            table.insert(messagesToSend, message)
            table.insert(messagesToRemoveIndices, i)
        else
            Logger:Debug("Skipping already sent message: " .. message.message)
            -- Add to remove indices even if already sent - we don't want to keep it in the queue
            table.insert(messagesToRemoveIndices, i)
        end
    end

    -- If no messages to send after filtering, return early but still remove processed messages
    if #messagesToSend == 0 then
        Logger:Debug("No new messages to send after filtering out duplicates")

        -- Remove already processed messages from the queue
        local removed = 0
        for i = #messagesToRemoveIndices, 1, -1 do
            local indexToRemove = messagesToRemoveIndices[i] - removed
            if indexToRemove > 0 and indexToRemove <= #self.MessageQueue then
                table.remove(self.MessageQueue, indexToRemove)
                removed = removed + 1
            end
        end

        return
    end

    -- Log the messages being sent for debugging
    if self.Config.Debug then
        for i, message in ipairs(messagesToSend) do
            Logger:Debug("Sending message " .. i .. " with server_id: " .. message.server_id)
        end
    end

    -- Create a copy of the messages to send
    local messagesToSendCopy = {}
    for i, message in ipairs(messagesToSend) do
        messagesToSendCopy[i] = message
    end

    -- Store the indices to remove for later use
    local indicesToRemove = {}
    for i, index in ipairs(messagesToRemoveIndices) do
        indicesToRemove[i] = index
    end

    -- Create a new promise to handle the entire operation
    return HTTP:Promise(function(resolve, reject)
        -- Get authentication headers
        Auth:GetAuthHeaders()
            :andThen(function(headers)
                -- Log the endpoint and payload for debugging
                Logger:Debug("Sending chat logs to endpoint: " .. self.Config.Endpoint)
                Logger:Debug("Payload: " .. HttpService:JSONEncode({messages = messagesToSendCopy}))

                -- Make request with authentication headers
                HTTP:Post(self.Config.Endpoint, {messages = messagesToSendCopy}, headers)
                    :andThen(function(response)
                        -- Process response
                        Logger:Info("Successfully sent " .. #messagesToSendCopy .. " chat messages to backend")

                        -- Log the response for debugging
                        if self.Config.Debug then
                            if response then
                                local responseStr = ""
                                if type(response) == "table" then
                                    -- Try to encode the response as JSON, but handle any errors
                                    local success, result = pcall(function()
                                        return HttpService:JSONEncode(response)
                                    end)

                                    if success then
                                        responseStr = result
                                    else
                                        responseStr = "Failed to encode response: " .. tostring(result)
                                    end
                                else
                                    responseStr = tostring(response)
                                end

                                Logger:Debug("Response from backend: " .. responseStr)
                            else
                                Logger:Debug("No response data from backend")
                            end
                        end

                        -- Mark messages as successfully sent
                        for _, message in ipairs(messagesToSendCopy) do
                            -- Track by both server_id and message content
                            if message.server_id then
                                self.SentMessages[message.server_id] = true
                            end

                            -- Also track by message content for duplicate detection
                            if message.sender_userid and message.message and message.timestamp then
                                local messageKey = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp
                                self.SentMessages[messageKey] = true
                            end
                        end

                        -- Check if we need to clear the cache based on size or time
                        local sentMessagesCount = 0
                        for _ in pairs(self.SentMessages) do
                            sentMessagesCount = sentMessagesCount + 1
                        end

                        local currentTime = os.time()
                        local timeSinceLastClear = currentTime - (self.LastCacheClearTime or 0)

                        -- Clear cache if it's too large or if the clear interval has passed
                        if sentMessagesCount > self.SentMessagesMaxSize or timeSinceLastClear > self.Config.CacheClearInterval then
                            Logger:Debug("Clearing sent messages cache (size: " .. sentMessagesCount ..
                                         ", time since last clear: " .. timeSinceLastClear .. " seconds)")
                            self.SentMessages = {}
                            self.MessageContentMap = {}
                            self.LastCacheClearTime = currentTime
                        end

                        -- Remove sent messages from queue AFTER successful sending
                        -- Use the stored indices to avoid issues with the queue changing
                        local removed = 0
                        for i = #indicesToRemove, 1, -1 do
                            local indexToRemove = indicesToRemove[i] - removed
                            if indexToRemove > 0 and indexToRemove <= #self.MessageQueue then
                                table.remove(self.MessageQueue, indexToRemove)
                                removed = removed + 1
                            end
                        end

                        Logger:Debug("Removed " .. removed .. " messages from queue after successful send")
                        Logger:Debug("Queue size after removal: " .. #self.MessageQueue)

                        resolve(response)
                    end)
                    :catch(function(err)
                        Logger:Warning("Failed to send chat messages to backend: " .. err)
                        Logger:Debug("Error details for chat message send: " .. tostring(err))

                        -- Check if the error is related to UUID format
                        if tostring(err):find("uuid") or tostring(err):find("UUID") then
                            Logger:Warning("UUID format error detected. Attempting to fix UUID formats.")

                            -- Try to fix the UUID formats for each message
                            for _, msg in ipairs(messagesToSendCopy) do
                                -- Ensure server_id is in proper UUID format
                                if msg.server_id and not string.match(msg.server_id, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
                                    Logger:Warning("Fixing invalid server_id format for message: " .. msg.message)
                                    msg.server_id = self:CreateDeterministicUUID(msg.sender_userid .. ":" .. msg.message .. ":" .. msg.timestamp)
                                end

                                -- Ensure client_uuid is in proper UUID format
                                if msg.client_uuid and not string.match(msg.client_uuid, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
                                    Logger:Warning("Fixing invalid client_uuid format for message: " .. msg.message)
                                    -- Remove any existing hyphens and reformat
                                    local rawUUID = string.gsub(msg.client_uuid, "-", "")
                                    if #rawUUID >= 32 then
                                        msg.client_uuid = string.sub(rawUUID, 1, 8) .. "-" ..
                                                         string.sub(rawUUID, 9, 12) .. "-" ..
                                                         string.sub(rawUUID, 13, 16) .. "-" ..
                                                         string.sub(rawUUID, 17, 20) .. "-" ..
                                                         string.sub(rawUUID, 21, 32)
                                        Logger:Info("Fixed client_uuid format: " .. msg.client_uuid)
                                    else
                                        -- If we can't fix it, generate a new one
                                        local newUUID = HttpService:GenerateGUID(false)
                                        -- Format properly
                                        newUUID = string.gsub(newUUID, "(%x%x%x%x%x%x%x%x)(%x%x%x%x)(%x%x%x%x)(%x%x%x%x)(%x%x%x%x%x%x%x%x%x%x%x%x)", "%1-%2-%3-%4-%5")
                                        msg.client_uuid = newUUID
                                        Logger:Info("Generated new client_uuid: " .. msg.client_uuid)
                                    end
                                end
                            end

                            -- Try again with fixed UUIDs
                            Logger:Info("Retrying with fixed UUID formats")
                            HTTP:Post(self.Config.Endpoint, {messages = messagesToSendCopy}, headers)
                                :andThen(function(response)
                                    Logger:Info("Successfully sent " .. #messagesToSendCopy .. " chat messages to backend after UUID fix")

                                    -- Mark messages as successfully sent
                                    for _, message in ipairs(messagesToSendCopy) do
                                        -- Track by both server_id and message content
                                        if message.server_id then
                                            self.SentMessages[message.server_id] = true
                                        end

                                        -- Also track by message content for duplicate detection
                                        if message.sender_userid and message.message and message.timestamp then
                                            local messageKey = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp
                                            self.SentMessages[messageKey] = true
                                        end
                                    end

                                    -- Check if we need to clear the cache based on size or time
                                    local sentMessagesCount = 0
                                    for _ in pairs(self.SentMessages) do
                                        sentMessagesCount = sentMessagesCount + 1
                                    end

                                    local currentTime = os.time()
                                    local timeSinceLastClear = currentTime - (self.LastCacheClearTime or 0)

                                    -- Clear cache if it's too large or if the clear interval has passed
                                    if sentMessagesCount > self.SentMessagesMaxSize or timeSinceLastClear > self.Config.CacheClearInterval then
                                        Logger:Debug("Clearing sent messages cache after UUID retry (size: " .. sentMessagesCount ..
                                                     ", time since last clear: " .. timeSinceLastClear .. " seconds)")
                                        self.SentMessages = {}
                                        self.MessageContentMap = {}
                                        self.LastCacheClearTime = currentTime
                                    end

                                    -- Remove sent messages from queue AFTER successful sending
                                    local removed = 0
                                    for i = #indicesToRemove, 1, -1 do
                                        local indexToRemove = indicesToRemove[i] - removed
                                        if indexToRemove > 0 and indexToRemove <= #self.MessageQueue then
                                            table.remove(self.MessageQueue, indexToRemove)
                                            removed = removed + 1
                                        end
                                    end

                                    Logger:Debug("Removed " .. removed .. " messages from queue after UUID fix and successful send")
                                    Logger:Debug("Queue size after removal: " .. #self.MessageQueue)

                                    resolve(response)
                                end)
                                :catch(function(retryErr)
                                    Logger:Error("Still failed after UUID fix: " .. tostring(retryErr))
                                    -- Messages remain in the queue for retry
                                    reject(retryErr)
                                end)
                        else
                            -- Messages remain in the queue for retry
                            reject(err)
                        end
                    end)
            end)
            :catch(function(err)
                Logger:Warning("Failed to get auth headers: " .. tostring(err))
                -- Messages remain in the queue for retry
                reject(err)
            end)
    end)
end

-- Set up shutdown handler
function Chatlog:SetupShutdownHandler()
    -- Get game service
    local game = game

    -- Connect to game:BindToClose event
    game:BindToClose(function()
        -- Check if there are messages in the queue
        if #self.MessageQueue > 0 then
            Logger:Info("Game shutting down, sending " .. #self.MessageQueue .. " remaining chat messages")

            -- Send all remaining messages
            self:SendAllChatLogs()
        end
    end)
end

-- Send all chat logs in the queue
function Chatlog:SendAllChatLogs()
    -- Check if there are messages to send
    if #self.MessageQueue == 0 then
        return
    end

    Logger:Info("Sending all " .. #self.MessageQueue .. " chat messages in queue")

    -- Create a copy of all messages in the queue, filtering out already sent messages
    local allMessages = {}
    local processedCount = 0

    for i, message in ipairs(self.MessageQueue) do
        processedCount = processedCount + 1

        -- Skip if message is nil
        if not message then
            continue
        end

        -- Validate server_id before sending
        if not message.server_id or message.server_id == "" then
            -- Generate a deterministic UUID if server_id is missing or empty
            local contentToHash = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp
            message.server_id = self:CreateDeterministicUUID(contentToHash)
            Logger:Debug("Generated deterministic server_id for message: " .. message.server_id)
        end

        -- Ensure timestamp is in the correct format
        if type(message.timestamp) == "number" then
            message.timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ", message.timestamp)
            Logger:Debug("Converted numeric timestamp to formatted string: " .. message.timestamp)
        end

        -- Create a message key for duplicate detection
        local messageKey = message.sender_userid .. ":" .. message.message .. ":" .. message.timestamp

        -- Only include messages that haven't been sent successfully
        if not self.SentMessages[message.server_id] and not self.SentMessages[messageKey] then
            table.insert(allMessages, message)
        else
            Logger:Debug("Skipping already sent message during shutdown: " .. message.message)
        end
    end

    -- If no messages to send after filtering, return early
    if #allMessages == 0 then
        Logger:Info("No new messages to send after filtering out duplicates during shutdown")
        self.MessageQueue = {} -- Clear the queue since all messages have been processed
        return
    end

    Logger:Info("Sending " .. #allMessages .. " unique chat messages during shutdown (processed " .. processedCount .. " total messages)")

    -- Make a backup of the messages we're about to send
    local messagesToSend = {}
    for i, message in ipairs(allMessages) do
        messagesToSend[i] = message
    end

    -- Clear the queue immediately to prevent duplicates in case of errors
    self.MessageQueue = {}

    -- Send messages in batches to avoid overwhelming the server
    local batchSize = self.Config.BatchSize
    local batches = math.ceil(#allMessages / batchSize)

    for i = 1, batches do
        local startIdx = (i - 1) * batchSize + 1
        local endIdx = math.min(i * batchSize, #allMessages)

        -- Create batch
        local batch = {}
        for j = startIdx, endIdx do
            table.insert(batch, allMessages[j])
        end

        -- Send batch synchronously
        Logger:Info("Sending shutdown batch " .. i .. " of " .. batches .. " (" .. #batch .. " messages)")

        -- Use pcall to catch any errors
        local success, result = pcall(function()
            -- Get authentication headers synchronously
            local headers = nil
            local authSuccess, authResult = pcall(function()
                -- This is a synchronous version of the async call
                local timestamp = os.time()
                local authToken = Auth:GenerateTimeBasedHash(timestamp)

                return {
                    ["Authorization"] = "Bearer " .. authToken,
                    ["X-Timestamp"] = tostring(timestamp)
                }
            end)

            if not authSuccess then
                Logger:Error("Failed to get auth headers during shutdown: " .. tostring(authResult))
                return false
            end

            headers = authResult

            -- Ensure all UUIDs are in proper format and timestamps are formatted correctly
            for _, msg in ipairs(batch) do
                -- Fix server_id format
                if msg.server_id and not string.match(msg.server_id, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
                    Logger:Warning("Fixing invalid server_id format for message during shutdown: " .. msg.message)
                    msg.server_id = self:CreateDeterministicUUID(msg.sender_userid .. ":" .. msg.message .. ":" .. msg.timestamp)
                end

                -- Fix client_uuid format
                if msg.client_uuid and not string.match(msg.client_uuid, "^%x%x%x%x%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%-%x%x%x%x%x%x%x%x%x%x%x%x$") then
                    Logger:Warning("Fixing invalid client_uuid format for message during shutdown: " .. msg.message)
                    -- Remove any existing hyphens and reformat
                    local rawUUID = string.gsub(msg.client_uuid, "-", "")
                    if #rawUUID >= 32 then
                        msg.client_uuid = string.sub(rawUUID, 1, 8) .. "-" ..
                                         string.sub(rawUUID, 9, 12) .. "-" ..
                                         string.sub(rawUUID, 13, 16) .. "-" ..
                                         string.sub(rawUUID, 17, 20) .. "-" ..
                                         string.sub(rawUUID, 21, 32)
                        Logger:Info("Fixed client_uuid format during shutdown: " .. msg.client_uuid)
                    else
                        -- If we can't fix it, generate a new one
                        local newUUID = HttpService:GenerateGUID(false)
                        -- Format properly
                        newUUID = string.gsub(newUUID, "(%x%x%x%x%x%x%x%x)(%x%x%x%x)(%x%x%x%x)(%x%x%x%x)(%x%x%x%x%x%x%x%x%x%x%x%x)", "%1-%2-%3-%4-%5")
                        msg.client_uuid = newUUID
                        Logger:Info("Generated new client_uuid during shutdown: " .. msg.client_uuid)
                    end
                end

                -- Fix timestamp format
                if type(msg.timestamp) == "number" then
                    msg.timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ", msg.timestamp)
                    Logger:Debug("Converted numeric timestamp to formatted string during shutdown: " .. msg.timestamp)
                end
            end

            -- Log the endpoint and payload for debugging
            Logger:Debug("Sending chat logs to endpoint during shutdown: " .. self.Config.Endpoint)

            -- Make HTTP request synchronously
            local httpSuccess, httpResult = pcall(function()
                -- Use HttpService directly for synchronous request
                local response = HttpService:PostAsync(
                    HTTP.Config.BaseURL .. self.Config.Endpoint,
                    HttpService:JSONEncode({messages = batch}),
                    Enum.HttpContentType.ApplicationJson,
                    false,
                    headers
                )

                return HttpService:JSONDecode(response)
            end)

            if not httpSuccess then
                Logger:Error("Failed to send chat messages during shutdown: " .. tostring(httpResult))
                return false
            end

            return true
        end)

        if success and result then
            Logger:Info("Successfully sent shutdown batch " .. i)
        else
            Logger:Warning("Failed to send shutdown batch " .. i .. " - messages may be lost")
        end

        -- Small delay to avoid rate limiting
        wait(0.5)
    end

    Logger:Info("Finished sending all chat messages during shutdown")
end

-- Return the module
return Chatlog
