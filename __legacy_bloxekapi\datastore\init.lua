--[[
    DataStore Module

    Handles interaction with Roblox DataStore.
    Provides a fallback mechanism when the backend server is unavailable.
]]

local DataStoreService = game:GetService("DataStoreService")

-- Module table
local DataStore = {}

-- Default configuration
DataStore.Config = {
    StoreName = "PlayerData",
    Scope = "Global",
    RetryCount = 3,
    RetryDelay = 1,
    Debug = false
}

-- DataStore instance
DataStore.Store = nil

-- Initialize the DataStore module
function DataStore:Initialize(config)
    -- Apply configuration
    if config then
        for key, value in pairs(config) do
            self.Config[key] = value
        end
    end

    -- Get DataStore
    self.Store = DataStoreService:GetDataStore(self.Config.StoreName, self.Config.Scope)

    return self
end

-- Create a Promise
function DataStore:Promise(callback)
    -- Get HTTP module for Promise implementation
    local HTTP = require(script.Parent.api.http)
    return HTTP:Promise(callback)
end

-- Get player data from DataStore
function DataStore:GetPlayerData(userId)
    return self:Promise(function(resolve, reject)
        -- Make sure we have a DataStore
        if not self.Store then
            reject("DataStore not initialized")
            return
        end

        -- Function to get data with retry logic
        local function getData(retryCount)
            local success, result = pcall(function()
                return self.Store:GetAsync("Player_" .. userId)
            end)

            if success then
                -- Debug logging
                if self.Config.Debug then
                    print("[DataStore] Got data for player " .. userId)
                end

                -- Return data or default
                resolve(result or {})
            else
                -- Debug logging
                if self.Config.Debug then
                    print("[DataStore] Failed to get data for player " .. userId .. ": " .. tostring(result))
                end

                -- Retry logic
                if retryCount > 0 then
                    wait(self.Config.RetryDelay)
                    getData(retryCount - 1)
                else
                    reject("Failed to get player data: " .. tostring(result))
                end
            end
        end

        -- Start getting data
        getData(self.Config.RetryCount)
    end)
end

-- Set player data in DataStore
function DataStore:SetPlayerData(userId, data)
    return self:Promise(function(resolve, reject)
        -- Make sure we have a DataStore
        if not self.Store then
            reject("DataStore not initialized")
            return
        end

        -- Function to set data with retry logic
        local function setData(retryCount)
            local success, result = pcall(function()
                return self.Store:SetAsync("Player_" .. userId, data)
            end)

            if success then
                -- Debug logging
                if self.Config.Debug then
                    print("[DataStore] Set data for player " .. userId)
                end

                resolve(true)
            else
                -- Debug logging
                if self.Config.Debug then
                    print("[DataStore] Failed to set data for player " .. userId .. ": " .. tostring(result))
                end

                -- Retry logic
                if retryCount > 0 then
                    wait(self.Config.RetryDelay)
                    setData(retryCount - 1)
                else
                    reject("Failed to set player data: " .. tostring(result))
                end
            end
        end

        -- Start setting data
        setData(self.Config.RetryCount)
    end)
end

-- Return the module
return DataStore
