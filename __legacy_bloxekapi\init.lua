--[[
    Roblox Client Agent

    Main entry point for the client agent that handles player data synchronization
    between the Roblox game and the backend server.

    Features:
    - Player data loading when a player joins
    - Data synchronization with the backend server
    - Fallback to Roblox DataStore when the backend is unavailable
    - Chat logging and monitoring
    - Automatic retry mechanisms for failed requests
    - Caching for improved performance
]]

local profileService = require(game.ServerScriptService.Server.Services.ProfileService)
local ServerScriptService = game:GetService("ServerScriptService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local MessagingService = game:GetService("MessagingService")
local HttpService = game:GetService("HttpService")

local Packages = game.ReplicatedStorage:WaitForChild('Packages')



-- Import modules
local API = require(script.api)
local DataStore = require(script.datastore)
local Utils = require(script.utils)
local Logger = require(script.utils.logger)
local Config = require(script.utils.config)
local HTTP = require(script.api.http)
local Chatlog = require(script.chatlog)

local promise = require(Packages.Promise)
local knit = require(Packages.knit)

-- Module table
local ClientAgent = knit.CreateService { Name = "bloxekapi", Client = {} }

-- Cache for player data
ClientAgent.PlayerDataCache = {}

-- Client ready var
ClientAgent.isActive = false

-- Temp devroom var
ClientAgent.DevRoomActive = true

-- Backend status tracking
ClientAgent.BackendStatus = {
    isAvailable = true,
    lastCheckTime = 0,
    checkInterval = 30, -- seconds
    usingBackupDataStore = {}, -- Track which players are using backup data
    pendingSyncToBackend = {} -- Track which players need to sync data to backend
}

-- Initialize the client agent
function ClientAgent:Initialize(config)
	Logger:Info("Initializing Roblox Client Agent")

	-- Apply configuration
	if config then
		Config:ApplyConfig(config)
	end

	for _, player in pairs(game.Players:GetChildren()) do
		self:OnPlayerJoin(player)
	end

	-- Set up player join/leave events
	Players.PlayerAdded:Connect(function(player)
		self:OnPlayerJoin(player)
	end)

	Players.PlayerRemoving:Connect(function(player)
		self:OnPlayerLeave(player)
	end)

	-- Initialize API
	API:Initialize(Config:GetAPIConfig())

	-- Initialize DataStore fallback
	DataStore:Initialize(Config:GetDataStoreConfig())

	-- Initialize Chatlog module
	Chatlog:Initialize(Config:GetChatlogConfig())

	-- Set up periodic backend availability check and data synchronization
	self:SetupPeriodicBackendCheck()

	Logger:Info("Roblox Client Agent initialized successfully")

	self.isActive = true

	return self
end

-- Set up periodic backend availability check
function ClientAgent:SetupPeriodicBackendCheck()
	Logger:Info("Setting up periodic backend availability check")

	-- Check interval in seconds
	local checkInterval = 60

	-- Create a loop that runs every checkInterval seconds
	task.spawn(function()
		while true do
			wait(checkInterval)

			-- Check if backend is available
			self:CheckBackendAvailability()
				:andThen(function(isAvailable)
					if isAvailable then
						-- If backend is available, sync any pending data
						local pendingCount = 0
						for _ in pairs(self.BackendStatus.pendingSyncToBackend) do
							pendingCount = pendingCount + 1
						end

						if pendingCount > 0 then
							Logger:Info("Found " .. pendingCount .. " players with pending data to sync")
							self:SyncPendingDataToBackend()
						end
					end
				end)
				:catch(function(err)
					Logger:Warning("Error during periodic backend check: " .. tostring(err))
				end)
		end
	end)

	Logger:Info("Periodic backend check setup complete")
end

-- Handle player join event
function ClientAgent:OnPlayerJoin(player)
	Logger:Info("Player joined: " .. player.Name .. " (UserId: " .. player.UserId .. ")")

	-- Load player data
	self:LoadPlayerData(player)
end

-- Release profile with retry logic
function ClientAgent:ReleaseProfileWithRetry(userId, playerName, retryCount)
    retryCount = retryCount or 0
    local maxRetries = 5
    local retryDelay = 2 -- seconds

    API.PlayerData:ReleasePlayerData(userId)
        :andThen(function()
            Logger:Info("Successfully released profile for " .. playerName)
        end)
        :catch(function(err)
            Logger:Warning("Failed to release profile for " .. playerName .. ": " .. tostring(err))

            if retryCount < maxRetries then
                Logger:Info("Retrying profile release in " .. retryDelay .. " seconds (attempt " .. (retryCount + 1) .. "/" .. maxRetries .. ")")
                wait(retryDelay)
                self:ReleaseProfileWithRetry(userId, playerName, retryCount + 1)
            else
                Logger:Error("Max retries reached for " .. playerName .. ", checking if player is in another server")
                -- Trigger the server messaging verification to force release if needed
                task.spawn(function()
                    self:CheckAndForceReleaseProfile(userId, playerName)
                end)
            end
        end)
end

-- Handle player leave event
function ClientAgent:OnPlayerLeave(player)
	Logger:Info("Player leaving: " .. player.Name .. " (UserId: " .. player.UserId .. ")")

	-- Save player data
	self:SavePlayerData(player)

	-- Release the profile with retry logic
	local userId = tostring(player.UserId)
	self:ReleaseProfileWithRetry(userId, player.Name)

	-- Clear from cache
	self.PlayerDataCache[player.UserId] = nil
end

-- Load player data from backend or fallback to DataStore
function ClientAgent:LoadPlayerData(player)
	local userId = tostring(player.UserId)

	-- First check if backend is available
	self:CheckBackendAvailability()
		:andThen(function(isAvailable)
			if not isAvailable then
				Logger:Info("Backend is unavailable, directly using DataStore for " .. player.Name)
				return self:FallbackToDataStore(player, userId)
			end

			-- Try to load player data from backend
			-- Note: The backend will automatically create a new player data entry if one doesn't exist
			return API.PlayerData:GetPlayerData(userId)
				:andThen(function(data)
					Logger:Info("Successfully loaded player data from backend for " .. player.Name)

					-- Remove from tracking lists if present
					self.BackendStatus.usingBackupDataStore[userId] = nil
					self.BackendStatus.pendingSyncToBackend[userId] = nil

					-- Validate the data
					if data then
						-- Check if we have a valid profile
						if data.profile and next(data.profile) ~= nil then
							Logger:Info("Valid profile found in player data for " .. player.Name)

							if not data.profile.Data.metadata['__migrated'] then data.profile.Data.metadata['__migrated'] = 0 end
							if data.profile.Data.metadata['__migrated'] < 1 then
								local PS_profile = gps:LoadProfileAsync("Player_" .. player.UserId, "ForceLoad")

								data.profile.Data = PS_profile.Data
								data.profile.Data.metadata['__migrated'] = 1

								PS_profile:Release()
							end
						elseif data.profile and next(data.profile) == nil then
							Logger:Warning("Empty profile received for " .. player.Name .. ", using default profile from API module")
						else
							Logger:Warning("No profile field found in player data for " .. player.Name .. ", data structure may be incorrect")
						end

						-- Store the data in cache
						self.PlayerDataCache[player.UserId] = data

						-- Apply the data to the player
						self:ApplyPlayerData(player, data)

						return data
					else
						-- If data is nil or invalid, fallback to DataStore
						Logger:Warning("Invalid data received from backend for " .. player.Name .. ", falling back to DataStore")
						return self:FallbackToDataStore(player, userId)
					end
				end)
				:catch(function(err)
					Logger:Warning("Failed to load player data from backend: " .. err)
					Logger:Info("Falling back to DataStore for " .. player.Name)

					-- Mark backend as potentially unavailable and check again
					self.BackendStatus.isAvailable = false
					self:CheckBackendAvailability()

					-- Fallback to DataStore
					return self:FallbackToDataStore(player, userId)
				end)
		end)
end

-- Helper function to fallback to DataStore
function ClientAgent:FallbackToDataStore(player, userId)
	-- Mark this player as using backup DataStore
	self.BackendStatus.usingBackupDataStore[userId] = true

	-- Also mark for syncing to backend when it becomes available
	self.BackendStatus.pendingSyncToBackend[userId] = true

	return DataStore:GetPlayerData(userId)
		:andThen(function(data)
			Logger:Info("Successfully loaded player data from DataStore for " .. player.Name)

			-- Format the data to match the expected structure from the backend
			local formattedData = {
				profile = {
					Data = data
				},
				-- Add any other fields that would normally come from the backend
				source = "datastore_fallback",
				timestamp = os.time()
			}

			self.PlayerDataCache[player.UserId] = formattedData
			self:ApplyPlayerData(player, formattedData)

			Logger:Info("Player " .. player.Name .. " is using backup DataStore data")
			return formattedData
		end)
		:catch(function(datastoreErr)
			Logger:Error("Failed to load player data from DataStore: " .. datastoreErr)

			-- Create default data
			local defaultData = self:CreateDefaultPlayerData(player)
			local formattedData = {
				profile = {
					Data = defaultData
				},
				source = "default_fallback",
				timestamp = os.time()
			}

			self.PlayerDataCache[player.UserId] = formattedData
			self:ApplyPlayerData(player, formattedData)

			Logger:Warning("Using default data for player " .. player.Name)
			return formattedData
		end)
end

-- Save player data to backend and DataStore
function ClientAgent:SavePlayerData(player)
	local userId = tostring(player.UserId)
	local playerData = self.PlayerDataCache[player.UserId]

	if not playerData then
		Logger:Warning("No player data to save for " .. player.Name)
		return
	end

	-- Update data with latest from player
	playerData = self:CollectPlayerData(player, playerData)

	-- Check if backend is available
	self:CheckBackendAvailability()
		:andThen(function(isAvailable)
			-- Always save to DataStore first for reliability
			return DataStore:SetPlayerData(userId, playerData.profile.Data)
				:andThen(function()
					Logger:Info("Successfully saved player data to DataStore for " .. player.Name)

					-- If backend is available, try to save there too
					if isAvailable then
						return API.PlayerData:UpdatePlayerData(userId, playerData)
							:andThen(function()
								Logger:Info("Successfully saved player data to backend for " .. player.Name)

								-- Clear from pending sync list if it was there
								self.BackendStatus.pendingSyncToBackend[userId] = nil
								self.BackendStatus.usingBackupDataStore[userId] = nil
							end)
							:catch(function(err)
								Logger:Warning("Failed to save player data to backend: " .. err)

								-- Mark for syncing later and as using backup
								self.BackendStatus.pendingSyncToBackend[userId] = true
								self.BackendStatus.usingBackupDataStore[userId] = true

								-- Mark backend as potentially unavailable and check again
								self.BackendStatus.isAvailable = false
								self:CheckBackendAvailability()
							end)
					else
						-- Backend is unavailable, mark for syncing later
						Logger:Info("Backend unavailable, player " .. player.Name .. " data will be synced later")
						self.BackendStatus.pendingSyncToBackend[userId] = true
						self.BackendStatus.usingBackupDataStore[userId] = true
					end
				end)
				:catch(function(err)
					Logger:Warning("Failed to save player data to DataStore: " .. err)

					-- If DataStore save failed but backend is available, try backend
					if isAvailable then
						return API.PlayerData:UpdatePlayerData(userId, playerData)
							:andThen(function()
								Logger:Info("Successfully saved player data to backend for " .. player.Name)
							end)
							:catch(function(backendErr)
								Logger:Error("Failed to save player data to both DataStore and backend: " ..
									err .. " / " .. backendErr)
							end)
					else
						Logger:Error("Failed to save player data to DataStore and backend is unavailable: " .. err)
					end
				end)
		end)
end

-- Create default player data
function ClientAgent:CreateDefaultPlayerData(player)
	return {
		["Level"] = 1,
		["badges"] =  {
		},
		["gamepasses"] =  {
		},
		["hasTransferredTimeData"] = false,
		["hasTransferredToDb"] = false,
		["metadata"] =  {
			["ChatsSent"] = 0,
			["LastJoin"] = 0,
			["RobuxSpent"] = 0,
			["SessionLoadCount"] = 0,
			["TimePlayed"] = 0,
			["__migrated"] = 0
		},
		["owned"] =  {
			["animations"] =  {
			},
			["badges"] =  {
			},
			["collectibles"] =  {
				["menofsnow"] =  {
				},
				["vibecoins"] =  {
				}
			},
			["gamepasses"] =  {
			},
			["items"] =  {
			},
			["rotech"] =  {
			},
			["tags"] =  {
			}
		},
		["settings"] =  {
			["fov"] = 70,
			["lowDetailMode"] = false,
			["musicVolume"] = 100,
			["ovhcolor"] = "1, 1, 1",
			["plrs"] = false,
			["potatoMode"] = false,
			["rainbow"] = true,
			["sfxVolume"] = 100,
			["tags"] = false,
			["volume"] = 50
		},
		["vip"] = false,
		["xp"] = 1
	}
end

-- Apply player data to the player
function ClientAgent:ApplyPlayerData(player, data)
	-- This function would apply the loaded data to the player
	-- Implementation depends on your game's specific needs

	-- Log the data structure for debugging
	Logger:Info("Applying player data for " .. player.Name)

	-- Validate the data structure
	if not data then
		Logger:Error("Cannot apply nil data for player " .. player.Name)
		return
	end

	-- Check if we have a profile field
	if not data.profile then
		Logger:Warning("No profile field in data for player " .. player.Name .. ", data structure may be incorrect")
	elseif next(data.profile) == nil then
		Logger:Warning("Empty profile for player " .. player.Name .. ", no data to apply")
	else
		Logger:Info("Valid profile found for player " .. player.Name)
	end

	-- Example: Set player stats if they exist
	if data.profile and data.profile.Data then
		local playerData = data.profile.Data

		-- Apply settings if they exist
		if playerData.settings then
			-- Example: Set FOV
			if playerData.settings.fov then
				-- Apply FOV setting to player
				Logger:Info("Setting FOV to " .. playerData.settings.fov .. " for player " .. player.Name)
			end
		end

		-- Apply level if it exists
		if playerData.Level then
			Logger:Info("Setting Level to " .. playerData.Level .. " for player " .. player.Name)
		end
	end

	-- Fire event to notify other systems that player data is loaded
	self:FirePlayerDataLoaded(player, data)
end

-- Collect current player data
function ClientAgent:CollectPlayerData(player, existingData)
	-- This function would collect current data from the player
	-- Implementation depends on your game's specific needs

	-- Example: Update stats in the existing data
	local updatedData = Utils:DeepCopy(existingData)

	-- Update last save time
	updatedData.lastSaved = os.time()

	return updatedData
end

-- Fire event when player data is loaded
function ClientAgent:FirePlayerDataLoaded(player, data)
	-- Create a bindable event if it doesn't exist
	if not self.PlayerDataLoadedEvent then
		self.PlayerDataLoadedEvent = Instance.new("BindableEvent")
		self.PlayerDataLoadedEvent.Name = "PlayerDataLoaded"
	end

	-- Fire the event
	self.PlayerDataLoadedEvent:Fire(player, data)
end

-- Get player data

--Client
function ClientAgent.Client:GetPlayerData(player)
	return HTTP:Promise(function(resolve, reject)
		resolve(ClientAgent:GetPlayerData(player.UserId))
	end)
end

--Server
function ClientAgent:GetPlayerData(player)
	local userId = typeof(player) == "number" and player or player.UserId

	if not self.PlayerDataCache[userId] then
		for i=1, 10 do
			Logger:Info('Could not find data for userid, retrying...')
			wait(1)
			if self.PlayerDataCache[userId] then break end
		end
	end

	if self.PlayerDataCache[userId] then return self.PlayerDataCache[userId].profile end
end

-- Update user settings

--Client
function ClientAgent.Client:UpdateUserSettings(player, setting, value)
	return HTTP:Promise(function(resolve, reject)
		resolve(ClientAgent:UpdateUserSettings(player.UserId, setting, value))
	end)
end

--Server
function ClientAgent:UpdateUserSettings(player, setting, value)
	local userId = typeof(player) == "number" and player or player.UserId

	-- Ensure player data exists in cache
	if not self.PlayerDataCache[userId] or not self.PlayerDataCache[userId].profile then
		Logger:Warning("Cannot update settings for player " .. tostring(userId) .. ": Player data not found in cache")
		return nil
	end

	-- Validate setting exists and type matches
	if self.PlayerDataCache[userId].profile.Data.settings[setting] == nil or
	   type(value) ~= type(self.PlayerDataCache[userId].profile.Data.settings[setting]) then
		Logger:Warning("Invalid setting or type mismatch: " .. setting)
		return nil
	end

	-- Update the setting in the player's data
	self.PlayerDataCache[userId].profile.Data.settings[setting] = value

	-- Log the update
	Logger:Info("Updated setting " .. setting .. " to " .. tostring(value) .. " for player " .. tostring(userId))

	-- Save the updated data to ensure persistence
	self:SavePlayerData(game.Players:GetPlayerByUserId(userId))

	return value
end

ClientAgent.Client.Active = ClientAgent.Active
function ClientAgent.Active()
	return promise.new(function(res,rej)
		if not ClientAgent.isActive then repeat wait(.1) until ClientAgent.isActive == true end
		res(true)
	end)
end

-- Check if a player is using backup datastore
function ClientAgent:IsUsingBackupDataStore(userId)
	if typeof(userId) ~= "string" then
		userId = tostring(userId)
	end
	return self.BackendStatus.usingBackupDataStore[userId] == true
end

-- Client function to check if using backup datastore
function ClientAgent.Client:IsUsingBackupDataStore(player)
	return HTTP:Promise(function(resolve)
		resolve(ClientAgent:IsUsingBackupDataStore(player.UserId))
	end)
end

-- Get backend status
function ClientAgent:GetBackendStatus()
	return {
		isAvailable = self.BackendStatus.isAvailable,
		lastCheckTime = self.BackendStatus.lastCheckTime,
		pendingSyncCount = Utils:TableCount(self.BackendStatus.pendingSyncToBackend),
		usingBackupCount = Utils:TableCount(self.BackendStatus.usingBackupDataStore)
	}
end

-- Client function to get backend status
function ClientAgent.Client:GetBackendStatus()
	return HTTP:Promise(function(resolve)
		resolve(ClientAgent:GetBackendStatus())
	end)
end

-- Check if a player is in another server using MessagingService
function ClientAgent:CheckPlayerInOtherServer(userId)
    return HTTP:Promise(function(resolve, reject)
        local topic = "PlayerPresence_" .. userId
        -- Use the JobId directly
        local serverId = game.JobId

        local messageData = {
            action = "query",
            serverId = serverId, -- Use JobId directly
            userId = userId,
            timestamp = os.time()
        }

        -- Track responses from other servers
        local playerFound = false
        local responseCount = 0
        local expectedResponses = 0 -- We don't know how many servers will respond
        local maxWaitTime = 5 -- seconds

        -- Function to check if we're done waiting
        local function checkComplete()
            if playerFound or (responseCount >= expectedResponses and expectedResponses > 0) then
                resolve(playerFound)
            end
        end

        -- Subscribe to responses
        local connection
        connection = MessagingService:SubscribeAsync(topic .. "_response", function(message)
            local data = HttpService:JSONDecode(message.Data)
            responseCount = responseCount + 1

            -- Check if player was found in the responding server
            if data.playerPresent then
                playerFound = true
                Logger:Info("Player " .. userId .. " found in server " .. data.respondingServerId)
            end

            checkComplete()
        end)

        -- Set timeout to resolve even if not all servers respond
        delay(maxWaitTime, function()
            connection:Disconnect()
            Logger:Info("Resolving player presence check for " .. userId .. ": " .. tostring(playerFound))
            resolve(playerFound)
        end)

        -- Send query to all servers
        MessagingService:PublishAsync(topic, HttpService:JSONEncode(messageData))
        Logger:Info("Published player presence query for " .. userId)
    end)
end

-- Force release a profile if the player is not in any other server
function ClientAgent:CheckAndForceReleaseProfile(userId, playerName)
    Logger:Info("Checking if player " .. playerName .. " is in another server")

    self:CheckPlayerInOtherServer(userId)
        :andThen(function(isInOtherServer)
            if not isInOtherServer then
                Logger:Info("Player " .. playerName .. " not found in any other server, forcing profile release")

                -- Use the direct unlock endpoint which bypasses all checks
                Logger:Info("Attempting direct unlock for profile of " .. playerName)

                -- Format the direct unlock endpoint
                local directUnlockEndpoint = string.format(API.PlayerData.Config.Endpoints.DirectUnlockPlayerData, userId)

                -- Get authentication headers
                local Auth = require(script.api.auth)
                local HTTP = require(script.api.http)

                Auth:GetAuthHeaders()
                    :andThen(function(headers)
                        -- Ensure the JobId is a valid UUID
                        local success, result = pcall(function()
                            local Chatlog = require(script.chatlog)
                            return Chatlog:JobIdToUUID(game.JobId)
                        end)

                        if success and result then
                            -- Use the UUID-formatted JobId
                            headers["X-Server-ID"] = result
                            Logger:Info("Using UUID-formatted JobId as server ID: " .. result)
                        else
                            -- Fallback to using the JobId directly (though this might cause issues)
                            headers["X-Server-ID"] = game.JobId
                            Logger:Warning("Failed to format JobId as UUID, using raw JobId: " .. game.JobId)
                        end

                        -- Make the direct unlock request
                        HTTP:Post(directUnlockEndpoint, {}, headers)
                            :andThen(function(response)
                                Logger:Info("Successfully unlocked profile for " .. playerName .. " using direct unlock")
                            end)
                            :catch(function(err)
                                Logger:Error("Failed to unlock profile using direct unlock: " .. tostring(err))

                                -- Try the force release endpoint
                                Logger:Info("Trying force release endpoint for " .. playerName)
                                local forceReleaseEndpoint = string.format(API.PlayerData.Config.Endpoints.ForceReleasePlayerData, userId)

                                HTTP:Post(forceReleaseEndpoint, {}, headers)
                                    :andThen(function(response)
                                        Logger:Info("Successfully force-released profile for " .. playerName)
                                    end)
                                    :catch(function(forceErr)
                                        Logger:Error("Failed to force-release profile: " .. tostring(forceErr))

                                        -- Last resort: try the two-step approach
                                        Logger:Info("Last resort: trying two-step approach for " .. playerName)

                                        -- Use the PlayerData module directly with force=true
                                        Logger:Info("Attempting to force get profile with PlayerData module")

                                        API.PlayerData:GetPlayerData(userId, 0, true) -- Use force=true
                                            :andThen(function(response)
                                                Logger:Info("Successfully forced ownership of profile for " .. playerName)

                                                -- Now release it
                                                local releaseEndpoint = string.format("/playerdata/roblox/%s/release", userId)
                                                return HTTP:Post(releaseEndpoint, {}, headers)
                                            end)
                                            :andThen(function(response)
                                                Logger:Info("Successfully released profile for " .. playerName .. " using two-step approach")
                                            end)
                                            :catch(function(stepErr)
                                                Logger:Error("All unlock attempts failed for " .. playerName .. ": " .. tostring(stepErr))
                                            end)
                                    end)
                            end)
                    end)
                    :catch(function(err)
                        Logger:Error("Failed to get auth headers for profile unlock: " .. tostring(err))
                    end)
            else
                Logger:Info("Player " .. playerName .. " is in another server, not forcing profile release")
            end
        end)
        :catch(function(err)
            Logger:Error("Error checking player presence: " .. tostring(err))
        end)
end

-- Check if the backend is available
function ClientAgent:CheckBackendAvailability()
    -- Don't check too frequently
    local currentTime = os.time()
    if currentTime - self.BackendStatus.lastCheckTime < self.BackendStatus.checkInterval then
        return HTTP:Promise(function(resolve)
            resolve(self.BackendStatus.isAvailable)
        end)
    end

    -- Update last check time
    self.BackendStatus.lastCheckTime = currentTime

    -- Make a simple request to check if the backend is available
    local endpoint = "/health" -- Assuming there's a health endpoint, if not use another lightweight endpoint

    return HTTP:Get(endpoint)
        :andThen(function(response)
            -- Backend is available
            local wasUnavailable = not self.BackendStatus.isAvailable
            self.BackendStatus.isAvailable = true

            -- If backend was previously unavailable, log that it's back
            if wasUnavailable then
                Logger:Info("Backend is now available, will sync pending data")
                self:SyncPendingDataToBackend()
            end

            return true
        end)
        :catch(function(err)
            -- Backend is unavailable
            local wasAvailable = self.BackendStatus.isAvailable
            self.BackendStatus.isAvailable = false

            -- If backend was previously available, log that it's down
            if wasAvailable then
                Logger:Warning("Backend is unavailable, will use DataStore fallback: " .. tostring(err))
            end

            return false
        end)
end

-- Sync pending data to backend when it becomes available
function ClientAgent:SyncPendingDataToBackend()
    Logger:Info("Syncing pending data to backend")

    -- Process each player that needs syncing
    for userId, _ in pairs(self.BackendStatus.pendingSyncToBackend) do
        local playerData = self.PlayerDataCache[tonumber(userId)]

        -- Skip if no data in cache
        if not playerData then
            Logger:Warning("No data to sync for user " .. userId)
            self.BackendStatus.pendingSyncToBackend[userId] = nil
            continue
        end

        -- Try to update data in backend
        Logger:Info("Syncing data for user " .. userId .. " to backend")

        API.PlayerData:UpdatePlayerData(userId, playerData)
            :andThen(function()
                Logger:Info("Successfully synced data for user " .. userId .. " to backend")
                self.BackendStatus.pendingSyncToBackend[userId] = nil
                self.BackendStatus.usingBackupDataStore[userId] = nil
            end)
            :catch(function(err)
                Logger:Warning("Failed to sync data for user " .. userId .. " to backend: " .. tostring(err))
                -- Keep in pending list for next sync attempt
            end)
    end
end

-- Set up server messaging to respond to player presence queries
function ClientAgent:SetupServerMessaging()
    Logger:Info("Setting up server messaging for player presence queries")

    -- Listen for queries about players in this server
    MessagingService:SubscribeAsync("PlayerPresence_*", function(message)
        local success, data = pcall(function()
            return HttpService:JSONDecode(message.Data)
        end)

        -- Ensure the JobId is a valid UUID
        local serverId = game.JobId
        local uuidSuccess, uuidResult = pcall(function()
            local Chatlog = require(script.chatlog)
            return Chatlog:JobIdToUUID(game.JobId)
        end)

        if uuidSuccess and uuidResult then
            serverId = uuidResult
        end

        if success and data and data.action == "query" and data.serverId ~= serverId then
            local userId = data.userId
            local playerPresent = Players:GetPlayerByUserId(tonumber(userId)) ~= nil

            -- Prepare response
            local responseData = {
                queryServerId = data.serverId,
                respondingServerId = serverId, -- Use JobId directly
                userId = userId,
                playerPresent = playerPresent,
                timestamp = os.time()
            }

            -- Send response
            local responseTopic = "PlayerPresence_" .. userId .. "_response"
            MessagingService:PublishAsync(responseTopic, HttpService:JSONEncode(responseData))

            Logger:Info("Responded to player presence query for " .. userId .. ": " .. tostring(playerPresent))
        end
    end)

    Logger:Info("Server messaging setup complete")
end

function ClientAgent:KnitStart()
	Signals = knit.GetService('signals')
	gps = profileService.GetProfileStore("playerdata1", ClientAgent:CreateDefaultPlayerData())

	Signals:CreateSignal('updateUserSettings'):Connect(function(player, setting, value)
		ClientAgent:UpdateUserSettings(player, setting, value)
	end)

	ClientAgent:Initialize()

    -- Set up server messaging after initialization
    self:SetupServerMessaging()
end

-- Return the module
return ClientAgent