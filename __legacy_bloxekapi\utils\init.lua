--[[
    Utils Module

    Utility functions for the Roblox client agent.
]]

-- Module table
local Utils = {}

-- Deep copy a table
function Utils:DeepCopy(original)
    local copy

    if type(original) == "table" then
        copy = {}
        for key, value in pairs(original) do
            copy[key] = self:DeepCopy(value)
        end
    else
        copy = original
    end

    return copy
end

-- Merge two tables
function Utils:MergeTables(t1, t2)
    local result = self:DeepCopy(t1)

    for key, value in pairs(t2) do
        if type(value) == "table" and type(result[key]) == "table" then
            result[key] = self:MergeTables(result[key], value)
        else
            result[key] = value
        end
    end

    return result
end

-- Format a number with commas
function Utils:FormatNumber(number)
    local formatted = tostring(number)
    local k

    while true do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", "%1,%2")
        if k == 0 then
            break
        end
    end

    return formatted
end

-- Format time (seconds to MM:SS)
function Utils:FormatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60

    return string.format("%02d:%02d", minutes, remainingSeconds)
end

-- Format date (Unix timestamp to string)
function Utils:FormatDate(timestamp)
    return os.date("%Y-%m-%d %H:%M:%S", timestamp)
end

-- Check if a value is in a table
function Utils:Contains(table, value)
    for _, v in pairs(table) do
        if v == value then
            return true
        end
    end

    return false
end

-- Get a random element from a table
function Utils:GetRandomElement(table)
    local keys = {}
    for key, _ in pairs(table) do
        keys[#keys + 1] = key
    end

    if #keys == 0 then
        return nil
    end

    local randomKey = keys[math.random(1, #keys)]
    return table[randomKey], randomKey
end

-- Count the number of elements in a table
function Utils:TableCount(tbl)
    local count = 0
    for _ in pairs(tbl) do
        count = count + 1
    end
    return count
end

-- Check if a table is empty
function Utils:IsTableEmpty(tbl)
    return next(tbl) == nil
end

-- Return the module
return Utils
